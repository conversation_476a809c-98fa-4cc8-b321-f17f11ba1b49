{"name": "ui", "version": "0.0.1", "scripts": {"start": "vite --clearScreen false --port $UI_PORT", "build": "bun run check && vite build", "check": "svelte-check --tsconfig ./tsconfig.json", "package": "bun run build && rimraf dist.zip && cd build && bestzip ../dist.zip *", "test": "vitest run", "test:unit": "vitest run tests/unit", "test:integration": "vitest run tests/integration", "test:e2e": "cross-env UI_PORT=5173 TAURI_DEV=true playwright test", "test:e2e:prod": "cross-env TAURI_DEV=false playwright test", "test:e2e:ui": "cross-env UI_PORT=5173 TAURI_DEV=true playwright test --ui", "test:e2e:debug": "cross-env UI_PORT=5173 TAURI_DEV=true playwright test --debug", "lint": "prettier --check . && eslint .", "format": "prettier --write ."}, "devDependencies": {"@apollo/client": "^3.13.8", "@effect/vitest": "^0.21.1", "@faker-js/faker": "^8.4.1", "@floating-ui/dom": "1.5.4", "@holochain/client": "^0.19.0", "@msgpack/msgpack": "^2.8.0", "@playwright/test": "^1.50.0", "@skeletonlabs/skeleton": "2.7.0", "@skeletonlabs/tw-plugin": "0.3.1", "@sveltejs/adapter-auto": "^3.3.1", "@sveltejs/adapter-static": "^3.0.8", "@sveltejs/kit": "^2.16.1", "@sveltejs/vite-plugin-svelte": "^4.0.4", "@tailwindcss/forms": "0.5.7", "@tailwindcss/typography": "0.5.10", "@types/bun": "latest", "@types/eslint": "8.56.0", "@types/node": "^22.10.10", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@valueflows/vf-graphql": "^0.9.0-alpha.10", "@valueflows/vf-graphql-holochain": "^0.0.3-alpha.10", "autoprefixer": "10.4.16", "bestzip": "^2.2.1", "cross-env": "^7.0.3", "effect": "^3.14.18", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-svelte": "^2.46.1", "graphql": "^16.8.0", "jsdom": "^26.1.0", "luxon": "^3.6.1", "moment-timezone": "^0.5.46", "playwright-electron": "^0.5.0", "postcss": "^8.5.1", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.5.14", "rimraf": "^6.0.1", "svelte": "^5.19.2", "svelte-check": "^3.8.6", "svelte-hero-icons": "^5.2.0", "svelte-fa": "^4.0.3", "tailwindcss": "^3.4.17", "tslib": "^2.8.1", "typescript": "^5.7.3", "vite": "^5.4.14", "vite-plugin-tailwind-purgecss": "^0.2.1", "vitest": "^3.1.2", "svelte-apollo": "^0.5.0"}, "type": "module", "dependencies": {"@types/luxon": "^3.6.2"}}