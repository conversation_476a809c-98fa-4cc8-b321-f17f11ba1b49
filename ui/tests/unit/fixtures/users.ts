import type { UIUser } from '$lib/types/ui';
import type { ActionHash, AgentPub<PERSON><PERSON> } from '@holochain/client';

const mockActionHash = new Uint8Array([0, 1, 2, 3]) as ActionHash;
const mockAgentPubKey = new Uint8Array([4, 5, 6, 7]) as AgentPubK<PERSON>;

export const testUsers: Record<string, UIUser> = {
  admin: {
    name: 'Admin User',
    nickname: 'admin',
    email: '<EMAIL>',
    user_type: 'creator',
    original_action_hash: mockActionHash,
    previous_action_hash: mockActionHash,
    agents: [mockAgentPubKey],
    status: {
      status_type: 'accepted',
      reason: 'Initial admin user'
    }
  },
  regular: {
    name: 'Regular User',
    nickname: 'user',
    email: '<EMAIL>',
    user_type: 'advocate',
    original_action_hash: mockActionHash,
    previous_action_hash: mockActionHash,
    agents: [mockAgentPubKey],
    status: {
      status_type: 'accepted'
    }
  }
};
