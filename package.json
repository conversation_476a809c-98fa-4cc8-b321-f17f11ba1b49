{"name": "requests_and_offers", "engines": {"node": ">=16.0.0"}, "packageManager": "bun@0.60.0", "private": true, "scripts": {"postinstall": "bun run download-hrea", "start": "AGENTS=${AGENTS:-2} BOOTSTRAP_PORT=$(get-port) bun run network", "network": "hc sandbox clean && bun run build:happ && UI_PORT=$(get-port) concurrently \"bun run --filter ui start\" \"bun run launch:happ\" \"hc playground\"", "test": "bun run build:zomes && hc app pack workdir --recursive && bun t -w tests && bun t -w ui && bun run test:status", "tests": "npm run test", "test:ui": "npm run build:zomes && hc app pack workdir --recursive && npm t -w ui", "test:unit": "cd ui && npm run test:unit", "test:integration": "cd ui && npm run test:integration", "test:misc": "npm run build:zomes && hc app pack workdir --recursive && npm t -w tests -- misc", "test:users": "npm run build:zomes && hc app pack workdir --recursive && npm t -w tests -- users", "test:administration": "npm run build:zomes && hc app pack workdir --recursive && npm t -w tests -- administration", "test:status": "cargo test --package administration_integrity --lib -- tests::status::status_tests --show-output", "test:organizations": "npm run build:zomes && hc app pack workdir --recursive && npm t -w tests -- organizations", "test:requests": "npm run build:zomes && hc app pack workdir --recursive && npm t -w tests -- requests-tests", "test:offers": "npm run build:zomes && hc app pack workdir --recursive && npm t -w tests -- offers-tests", "test:service-types": "npm run build:zomes && hc app pack workdir --recursive && npm t -w tests -- service-types-tests", "test:service-types:status": "npm run build:zomes && hc app pack workdir --recursive && npm t -w tests -- service-types-tests/status", "test:service-types:tags": "npm run build:zomes && hc app pack workdir --recursive && npm t -w tests -- service-types-tests/tag-functionality", "test:mediums-of-exchange": "npm run build:zomes && hc app pack workdir --recursive && npm t -w tests -- mediums-of-exchange-tests", "launch:happ": "hc-spin -n $AGENTS --ui-port $UI_PORT workdir/requests_and_offers.happ", "start:tauri": "AGENTS=${AGENTS:-2} BOOTSTRAP_PORT=$(get-port) SIGNAL_PORT=$(get-port) bun run network:tauri", "network:tauri": "hc sandbox clean && bun run build:happ && UI_PORT=$(get-port) concurrently \"bun run start --workspace ui\" \"bun run launch:tauri\" \"hc playground\"", "launch:tauri": "concurrently \"kitsune2-bootstrap-srv --listen \"127.0.0.1:$BOOTSTRAP_PORT\"\" \"echo pass | RUST_LOG=warn hc launch --piped -n $AGENTS workdir/movies5.happ --ui-port $UI_PORT network --bootstrap http://127.0.0.1:\"$BOOTSTRAP_PORT\" webrtc ws://127.0.0.1:\"$BOOTSTRAP_PORT\"\"", "package": "bun run build:happ && bun --filter ui package && hc web-app pack workdir --recursive", "build:happ": "bun run build:zomes && hc app pack workdir --recursive", "build:zomes": "RUSTFLAGS='' CARGO_TARGET_DIR=target cargo build --release --target wasm32-unknown-unknown", "download-hrea": "[ ! -f \"workdir/hrea.dna\" ] && curl -L --output workdir/hrea.dna https://github.com/h-REA/hREA/releases/download/happ-0.3.2-beta/hrea.dna; exit 0", "clean:hrea": "rimraf workdir/hrea.dna", "check": "cd ui && bun run check"}, "devDependencies": {"@holochain/hc-spin": "0.500.0", "caniuse-lite": "^1.0.30001727", "concurrently": "^6.5.1", "get-port-cli": "^3.0.0", "rimraf": "^6.0.1"}, "workspaces": ["ui", "tests"]}