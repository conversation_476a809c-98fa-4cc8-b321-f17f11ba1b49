[profile.dev]
opt-level = "z"

[profile.release]
opt-level = "z"

[workspace]
members = [
  "dnas/requests_and_offers/utils",
  "dnas/*/zomes/coordinator/*",
  "dnas/*/zomes/integrity/*",
]
resolver = "2"

[workspace.dependencies]
hdi = "=0.6.3"
hdk = "=0.5.3"
holochain_serialized_bytes = "*"
thiserror = "^2.0"
serde = "^1.0"
chrono = "^0.3"                  # 0.4 compatibility error with wasm_bindgen  


[workspace.dependencies.utils]
path = "dnas/requests_and_offers/utils"

[workspace.dependencies.misc]
path = "dnas/requests_and_offers/zomes/coordinator/misc"

[workspace.dependencies.administration]
path = "dnas/requests_and_offers/zomes/coordinator/administration"

[workspace.dependencies.administration_integrity]
path = "dnas/requests_and_offers/zomes/integrity/administration"

[workspace.dependencies.users_organizations]
path = "dnas/requests_and_offers/zomes/coordinator/users_organizations"

[workspace.dependencies.users_organizations_integrity]
path = "dnas/requests_and_offers/zomes/integrity/users_organizations"

[workspace.dependencies.requests]
path = "dnas/requests_and_offers/zomes/coordinator/requests"

[workspace.dependencies.requests_integrity]
path = "dnas/requests_and_offers/zomes/integrity/requests"

[workspace.dependencies.offers]
path = "dnas/requests_and_offers/zomes/coordinator/offers"

[workspace.dependencies.offers_integrity]
path = "dnas/requests_and_offers/zomes/integrity/offers"

[workspace.dependencies.service_types]
path = "dnas/requests_and_offers/zomes/coordinator/service_types"

[workspace.dependencies.service_types_integrity]
path = "dnas/requests_and_offers/zomes/integrity/service_types"

[workspace.dependencies.mediums_of_exchange]
path = "dnas/requests_and_offers/zomes/coordinator/mediums_of_exchange"

[workspace.dependencies.mediums_of_exchange_integrity]
path = "dnas/requests_and_offers/zomes/integrity/mediums_of_exchange"
