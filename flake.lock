{"nodes": {"crane": {"locked": {"lastModified": 1750266157, "narHash": "sha256-tL42YoNg9y30u7zAqtoGDNdTyXTi8EALDeCB13FtbQA=", "owner": "<PERSON><PERSON><PERSON>", "repo": "crane", "rev": "e37c943371b73ed87faf33f7583860f81f1d5a48", "type": "github"}, "original": {"owner": "<PERSON><PERSON><PERSON>", "repo": "crane", "type": "github"}}, "flake-parts": {"inputs": {"nixpkgs-lib": "nixpkgs-lib"}, "locked": {"lastModified": 1749398372, "narHash": "sha256-tYBdgS56eXYaWVW3fsnPQ/nFlgWi/Z2Ymhyu21zVM98=", "owner": "hercules-ci", "repo": "flake-parts", "rev": "9305fe4e5c2a6fcf5ba6a3ff155720fbe4076569", "type": "github"}, "original": {"owner": "hercules-ci", "repo": "flake-parts", "type": "github"}}, "hc-launch": {"flake": false, "locked": {"lastModified": 1745512267, "narHash": "sha256-oVXdsyVJQ/LAwrgaPXN2B9ixW8KpKXoR+kq51hDxp6o=", "owner": "holochain", "repo": "hc-launch", "rev": "ef3b8fa222a81711351dc913c7d166564f03112c", "type": "github"}, "original": {"owner": "holochain", "ref": "holochain-0.5", "repo": "hc-launch", "type": "github"}}, "hc-scaffold": {"flake": false, "locked": {"lastModified": 1747059984, "narHash": "sha256-gRj4/ptoQ4hb3rwkZ/gX3+OhN9NmhJ42XTrOlnz6QpE=", "owner": "holochain", "repo": "scaffolding", "rev": "54ec9b851db94f2e9489b1cae683032f7407be84", "type": "github"}, "original": {"owner": "holochain", "ref": "holochain-0.5", "repo": "scaffolding", "type": "github"}}, "holochain": {"flake": false, "locked": {"lastModified": 1750866060, "narHash": "sha256-H9K+83vNPRRIV8e3HmLEHqdx0w03e8aiEF2NhYLXbD0=", "owner": "holochain", "repo": "holochain", "rev": "8061d5b02f8eb03abfb6942b0a502d0d9bd3249f", "type": "github"}, "original": {"owner": "holochain", "ref": "holochain-0.5.3", "repo": "holochain", "type": "github"}}, "holonix": {"inputs": {"crane": "crane", "flake-parts": "flake-parts", "hc-launch": "hc-launch", "hc-scaffold": "hc-scaffold", "holochain": "holochain", "kitsune2": "kitsune2", "lair-keystore": "lair-keystore", "nixpkgs": "nixpkgs", "playground": "playground", "rust-overlay": "rust-overlay"}, "locked": {"lastModified": 1750882451, "narHash": "sha256-3rKsccGSU8GmMkgaqsvmsTJE2+g/faZMYP6GhL+kFq0=", "owner": "holochain", "repo": "holonix", "rev": "bd0705e5c72576eb5fa5210ebcaf0ec99373145d", "type": "github"}, "original": {"owner": "holochain", "ref": "main-0.5", "repo": "holonix", "type": "github"}}, "kitsune2": {"flake": false, "locked": {"lastModified": 1750424599, "narHash": "sha256-uY8f1hqUvS+8jd+2YKC0EPVG7HFEDXoWL0bOF7F1VQg=", "owner": "holochain", "repo": "kitsune2", "rev": "dfc57d4c3faeca1b12a5669776a93cd6b43e6d7c", "type": "github"}, "original": {"owner": "holochain", "ref": "v0.1.9", "repo": "kitsune2", "type": "github"}}, "lair-keystore": {"flake": false, "locked": {"lastModified": 1750349209, "narHash": "sha256-IUj2u8I2YSVZGrZ234mVCPENTg239LSG05TysoB3t+A=", "owner": "holochain", "repo": "lair", "rev": "978e3569e6a9cf674d2f1fc380cc82a87a43c78a", "type": "github"}, "original": {"owner": "holochain", "ref": "v0.6.2", "repo": "lair", "type": "github"}}, "nixpkgs": {"locked": {"lastModified": 1750622754, "narHash": "sha256-kMhs+YzV4vPGfuTpD3mwzibWUE6jotw5Al2wczI0Pv8=", "owner": "nixos", "repo": "nixpkgs", "rev": "c7ab75210cb8cb16ddd8f290755d9558edde7ee1", "type": "github"}, "original": {"owner": "nixos", "ref": "nixos-25.05", "repo": "nixpkgs", "type": "github"}}, "nixpkgs-lib": {"locked": {"lastModified": 1748740939, "narHash": "sha256-rQaysilft1aVMwF14xIdGS3sj1yHlI6oKQNBRTF40cc=", "owner": "nix-community", "repo": "nixpkgs.lib", "rev": "656a64127e9d791a334452c6b6606d17539476e2", "type": "github"}, "original": {"owner": "nix-community", "repo": "nixpkgs.lib", "type": "github"}}, "playground": {"flake": false, "locked": {"lastModified": 1748941883, "narHash": "sha256-YeLySFcUnDpbJPaNn5UF5LE3DlIYRlJLmknqqy7GHws=", "owner": "darksoil-studio", "repo": "holochain-playground", "rev": "9962a33ce1d00c9d0a32724644032852dd88a083", "type": "github"}, "original": {"owner": "darksoil-studio", "ref": "main-0.5", "repo": "holochain-playground", "type": "github"}}, "root": {"inputs": {"flake-parts": ["holonix", "flake-parts"], "holonix": "holonix", "nixpkgs": ["holonix", "nixpkgs"]}}, "rust-overlay": {"inputs": {"nixpkgs": ["holonix", "nixpkgs"]}, "locked": {"lastModified": 1750819193, "narHash": "sha256-XvkupGPZqD54HuKhN/2WhbKjAHeTl1UEnWspzUzRFfA=", "owner": "oxalica", "repo": "rust-overlay", "rev": "1ba3b9c59b68a4b00156827ad46393127b51b808", "type": "github"}, "original": {"owner": "oxalica", "repo": "rust-overlay", "type": "github"}}}, "root": "root", "version": 7}