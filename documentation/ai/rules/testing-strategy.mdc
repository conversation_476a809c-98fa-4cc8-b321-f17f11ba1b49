---
description: 
globs: ui/tests/**,tests/**
alwaysApply: false
---
# Testing Strategy

This document defines the comprehensive testing strategy for the Re<PERSON><PERSON> and Offers project, covering all testing layers and philosophies across the application stack.

## Testing Architecture Overview

The project implements a **3-Layer Testing Strategy**:

1. **Backend Integration Tests** (`tests/`) - Holochain zome testing with Tryorama
2. **Frontend Unit Tests** (`ui/tests/unit/`) - Isolated component and service testing
3. **Frontend Integration Tests** (`ui/tests/integration/`) - integration UI flow testing

## Core Testing Philosophy

### 1. **Test-Driven Quality Assurance**
- **Focus on public APIs** - Test behavior from consumer perspective, not implementation details
- **Isolation** - Tests should be independent with no external dependencies
- **Predictability** - Consistent results on every run
- **Readability** - Tests as living documentation

### 2. **Domain-Driven Testing**
Tests are organized by domain boundaries matching the application architecture:
- **Service Types** - Core taxonomy and moderation workflows
- **Requests & Offers** - Main marketplace functionality  
- **Users & Organizations** - Identity and relationship management
- **Administration** - Access control and entity status management

### 3. **Effect TS Testing Patterns**
All async operations use Effect TS patterns with specialized testing utilities:
- `mockEffectFn()` and `mockEffectFnWithParams()` for Effect-based functions
- `runEffect()` for executing Effects in tests with better error messages
- Dependency injection through `Effect.provide()` for service mocking

## Testing Commands

```bash
# Backend Tests (Tryorama)
cd tests && bun test

# Frontend Unit Tests  
cd ui && bun test:unit

# Frontend Integration Tests
cd ui && bun test:integration
```

## Test Organization Standards

### File Naming Convention
- **Backend**: `*.test.ts` in domain folders
- **Frontend Unit**: `*.test.ts` in `ui/tests/unit/`
- **Frontend Integration**: `*.test.ts` in `ui/tests/integration/`

### Test Structure Standard
```typescript
describe('Component or Module Name', () => {
  beforeEach(() => {
    // Setup code
  });

  afterEach(() => {
    // Cleanup code
  });

  it('should do something specific', () => {
    // Arrange
    // Act  
    // Assert
  });
});
```

## Cross-Layer Testing Coordination

### 1. **Data Consistency**
- Backend tests validate Holochain data integrity
- Frontend tests use mock data matching backend structures
- Integration tests verify end-to-end data flow

### 2. **Error Handling Verification**
- Backend tests verify zome function error scenarios
- Unit tests verify service layer error transformation
- Integration tests verify UI error display and recovery

### 3. **Security & Access Control**
- Backend tests verify all permission-based operations
- Frontend tests mock appropriate authorization states
- Integration tests verify complete access control flows

## Testing Quality Gates

### Coverage Expectations
- **Backend**: Focus on business logic completeness over coverage percentage
- **Unit Tests**: High coverage for services, stores, and utilities (>80%)
- **Integration**: Critical user journeys and error scenarios (key flows)

### Performance Standards
- **Backend Tests**: < 3 minutes per domain test suite
- **Unit Tests**: < 30 seconds total execution
- **Integration**: < 2 minutes per major workflow

## Maintenance Guidelines

### Test Maintenance
- Update tests when refactoring services or stores
- Maintain test data consistency across all layers
- Review and update mocks when service interfaces change

### Debugging Strategy
- Use `runEffect()` helper for better Effect error messages
- Leverage domain-specific test utilities in `common.ts` files
- Implement detailed assertion messages for complex scenarios
