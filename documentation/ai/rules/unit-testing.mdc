---
description: 
globs: ui/tests/unit/**
alwaysApply: false
---
# Frontend Unit Testing

This document defines the unit testing patterns for frontend services, stores, and components, with specialized support for Effect TS testing patterns and Svelte 5 component testing.

## Core Unit Testing Principles

### 1. **Isolation & Mocking**
- Test units in complete isolation using mocks for all dependencies
- Use Effect TS dependency injection for clean service mocking
- Mock external dependencies (HolochainClient, EventBus) consistently

### 2. **Effect TS Testing Patterns**
All async operations use Effect TS with specialized testing utilities located in `ui/tests/unit/effect.ts`:

```typescript
// Mock Effect functions
const mockServiceFn = mockEffectFn(vi.fn(() => Promise.resolve(data)));
const mockServiceWithParams = mockEffectFnWithParams(
  vi.fn((param) => Promise.resolve(result))
);

// Execute Effects in tests
const result = await runEffect(
  Effect.provide(effectToTest, mockLayer)
);
```

### 3. **Testing Commands**
```bash
# Run all unit tests
cd ui && bun test:unit

# Run specific test file
cd ui && bun test:unit components/service-types/ServiceTypeForm.test.ts

# Watch mode
cd ui && bun test:unit --watch
```

## Service Layer Testing

### **Service Testing Pattern**
Located in `ui/tests/unit/services/`

**Core Structure:**
```typescript
describe('ServiceName Service', () => {
  const createMockHolochainClientService = () => ({
    callZome: vi.fn(),
    callZomeRawEffect: vi.fn(),
    callZomeEffect: vi.fn()
  });

  const createTestLayer = (mockClient: ReturnType<typeof createMockHolochainClientService>) =>
    Layer.succeed(HolochainClientServiceTag, mockClient);

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should perform service operation', async () => {
    // Arrange
    const mockClient = createMockHolochainClientService();
    mockClient.callZomeEffect.mockResolvedValue(expectedResult);

    // Act
    const result = await runEffect(
      serviceOperation(params).pipe(
        E.provide(ServiceLive),
        E.provide(createTestLayer(mockClient))
      )
    );

    // Assert
    expect(result).toEqual(expectedResult);
    expect(mockClient.callZomeEffect).toHaveBeenCalledWith(...);
  });
});
```

**Key Testing Areas:**
- **Schema Validation**: Test input/output schema validation
- **Error Handling**: Test service error transformation
- **Dependency Injection**: Verify proper service composition
- **Zome Integration**: Mock Holochain client interactions

### **HolochainClient Service Testing**
Special patterns for testing the foundational HolochainClient service:

```typescript
// Test both raw and schema-validated zome calls
expect(mockClient.callZomeRawEffect).toHaveBeenCalledWith(
  'zome_name',
  'function_name', 
  payload
);

expect(mockClient.callZomeEffect).toHaveBeenCalledWith(
  'zome_name',
  'function_name',
  payload,
  expectedSchema
);
```

## Store Testing Patterns

### **Svelte Store Testing Structure**
Located in `ui/tests/unit/stores/`

**Standard Store Test Pattern:**
```typescript
describe('DomainStore', () => {
  const createMockService = (overrides: Partial<DomainService> = {}): DomainService => ({
    getAllEntities: mockEffectFn(vi.fn(() => Promise.resolve([]))),
    createEntity: mockEffectFnWithParams(vi.fn(() => Promise.resolve(entity))),
    updateEntity: mockEffectFnWithParams(vi.fn(() => Promise.resolve(entity))),
    deleteEntity: mockEffectFnWithParams(vi.fn(() => Promise.resolve(true))),
    ...overrides
  });

  const createStoreWithService = async (service: DomainService): Promise<DomainStore> => {
    const mockLayer = Layer.succeed(DomainServiceTag, service);
    return await runEffect(
      createDomainStore().pipe(E.provide(mockLayer))
    );
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Entity Management', () => {
    it('should load entities successfully', async () => {
      // Arrange
      const mockEntities = [mockEntity1, mockEntity2];
      const mockService = createMockService({
        getAllEntities: mockEffectFn(vi.fn(() => Promise.resolve(mockEntities)))
      });

      // Act
      const store = await createStoreWithService(mockService);
      await runEffect(store.loadEntities());

      // Assert
      expect(store.entities).toEqual(mockEntities);
      expect(store.loading).toBe(false);
      expect(store.error).toBeNull();
    });

    it('should handle errors gracefully', async () => {
      // Arrange
      const mockError = new Error('Service error');
      const mockService = createMockService({
        getAllEntities: mockEffectFn(vi.fn(() => Promise.reject(mockError)))
      });

      // Act
      const store = await createStoreWithService(mockService);
      await runEffect(store.loadEntities());

      // Assert
      expect(store.entities).toEqual([]);
      expect(store.loading).toBe(false);
      expect(store.error).toEqual(mockError);
    });
  });
});
```

**Store Testing Focus Areas:**
- **State Management**: Verify reactive state updates
- **Error Handling**: Test error state management
- **Loading States**: Verify loading indicator behavior
- **Cache Management**: Test EntityCache integration
- **Event Emission**: Verify EventBus integration with MockEventBus

### **EventBus Testing Pattern**
```typescript
// Create MockEventBus for testing
const mockEventBus = createMockEventBus<StoreEvents>();

// Test with EventBus layer
await runEffect(
  store.performAction(params).pipe(
    E.provide(mockEventBus.mockLayer)
  )
);

// Verify events were emitted
expect(mockEventBus.emitHistory).toHaveLength(1);
expect(mockEventBus.emitHistory[0].event).toBe('entity:created');
expect(mockEventBus.emitHistory[0].payload).toHaveProperty('entityId');
```

## Component Testing Patterns

### **Svelte Component Testing**
Located in `ui/tests/unit/components/`

**Component Test Structure:**
```typescript
describe('ComponentName', () => {
  // Mock store/service dependencies
  interface MockStore {
    entities: Entity[];
    loading: boolean;
    error: Error | null;
    performAction: (param: string) => E.Effect<unknown, Error>;
  }

  const createMockStore = (overrides: Partial<MockStore> = {}): MockStore => ({
    entities: [],
    loading: false,
    error: null,
    performAction: mockEffectFnWithParams(vi.fn(() => Promise.resolve())),
    ...overrides
  });

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render with initial state', () => {
    // Arrange
    const mockStore = createMockStore();

    // Act & Assert
    // Component rendering and interaction tests
    expect(mockStore.entities).toEqual([]);
    expect(mockStore.loading).toBe(false);
  });

  it('should handle user interactions', async () => {
    // Arrange
    const mockStore = createMockStore();
    const mockAction = vi.fn();

    // Act
    await runEffect(mockStore.performAction('test-param'));

    // Assert
    expect(mockAction).toHaveBeenCalledWith('test-param');
  });
});
```

**Component Testing Areas:**
- **Props & State**: Test component props and internal state
- **User Interactions**: Test click handlers, form submissions
- **Store Integration**: Test component-store communication
- **Error Display**: Test error message rendering
- **Loading States**: Test loading indicator display

### **Form Component Testing**
Special patterns for form components:

```typescript
describe('FormComponent', () => {
  it('should validate form inputs', () => {
    // Test form validation logic
    const validateForm = (formData: FormData) => {
      // Validation logic
    };

    expect(validateForm(invalidData)).toBe(false);
    expect(validateForm(validData)).toBe(true);
  });

  it('should handle form submission', async () => {
    // Test form submission with Effect
    const mockSubmit = mockEffectFnWithParams(
      vi.fn(() => Promise.resolve(submitResult))
    );

    await runEffect(mockSubmit(formData));
    expect(mockSubmit).toHaveBeenCalledWith(formData);
  });
});
```

## Testing Utilities & Helpers

### **Effect Testing Utilities**
Located in `ui/tests/unit/effect.ts`

**Core Utilities:**
```typescript
// Mock Effect functions
export const mockEffectSuccess = <T>(value: T): Effect.Effect<T>
export const mockEffectFailure = <E>(error: E): Effect.Effect<never, E>
export const mockEffectFn = <T, ErrorType = Error>(fn: () => Promise<T>)
export const mockEffectFnWithParams = <P extends unknown[], T, ErrorType = Error>()
```

### **Test Helpers**
Located in `ui/tests/unit/test-helpers.ts`

**Mock Data Generation:**
```typescript
// Generate test data
export async function createTestRequest(): Promise<RequestInput>
export async function createTestOffer(): Promise<OfferInput>
export function createTestServiceType(): ServiceTypeInDHT
export async function createMockRecord<T>(): Promise<Record>
```

### **Mock Services**
Located in `ui/tests/mocks/`

**Standardized Mocks:**
- `HolochainClientMock.ts` - Mock Holochain client
- `eventBus.mock.ts` - Mock event bus system  
- `services.mock.ts` - Mock service implementations

## Performance & Best Practices

### **Test Performance**
- Target: < 30 seconds total execution for all unit tests
- Use `vi.clearAllMocks()` in beforeEach for clean test isolation
- Avoid real async operations; use mocks exclusively

### **Debugging**
- Use `runEffect()` instead of `Effect.runPromise()` for better error messages
- Implement detailed assertion messages for complex scenarios
- Use `vi.fn()` to track function calls and parameters

### **Maintenance**
- Update tests when refactoring services or stores  
- Keep mock data consistent with actual data structures
- Review and update test utilities when patterns evolve
