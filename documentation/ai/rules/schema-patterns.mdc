# Schema Patterns

This document defines the standardized schema patterns using Effect `Schema` for validation, type safety, branded types, and data transformation across the application.

## Core Schema Principles

- **Effect Schema Foundation**: Use `Schema` from 'effect' package, never external schema libraries
- **Branded Type Safety**: Leverage branded types for domain-specific type safety
- **Class-Based Complex Schemas**: Use `Schema.Class` for complex data structures
- **Centralized Schema Organization**: Organized by domain with centralized exports
- **Strategic Validation**: Apply validation at appropriate boundaries
- **Type and Schema Exports**: Export both schemas and derived types

## Schema Architecture Overview

The schema system is organized into specialized modules:

### 1. Foundation Schemas
- **[holochain.schemas.ts](mdc:ui/src/lib/schemas/holochain.schemas.ts)**: Core Holochain type schemas and branded types
- **[ui.schemas.ts](mdc:ui/src/lib/schemas/ui.schemas.ts)**: UI state, pagination, and interface schemas
- **[form.schemas.ts](mdc:ui/src/lib/schemas/form.schemas.ts)**: Form validation and user input schemas

### 2. Domain-Specific Schemas
- **[service-types.schemas.ts](mdc:ui/src/lib/schemas/service-types.schemas.ts)**: Service Types domain schemas
- **Request/Offer Schemas**: Domain-specific validation and transformation schemas

### 3. Centralized Exports
- **[index.ts](mdc:ui/src/lib/schemas/index.ts)**: Single import point for all schemas

## Branded Type Patterns

### Holochain Branded Types

Use branded types for Holochain-specific data to ensure type safety:

```typescript
import { Schema } from 'effect';

// Core Holochain branded types
export const AgentPubKeySchema = Schema.String.pipe(
  Schema.brand('AgentPubKey'),
  Schema.annotations({
    title: 'Agent Public Key',
    description: 'A Holochain agent public key'
  })
);

export const ActionHashSchema = Schema.String.pipe(
  Schema.brand('ActionHash'),
  Schema.annotations({
    title: 'Action Hash', 
    description: 'A Holochain action hash'
  })
);

export const EntryHashSchema = Schema.String.pipe(
  Schema.brand('EntryHash'),
  Schema.annotations({
    title: 'Entry Hash',
    description: 'A Holochain entry hash'
  })
);

// Export the derived types
export type AgentPubKey = Schema.Schema.Type<typeof AgentPubKeySchema>;
export type ActionHash = Schema.Schema.Type<typeof ActionHashSchema>;
export type EntryHash = Schema.Schema.Type<typeof EntryHashSchema>;
```

### Domain-Specific Branded Types

Create branded types for domain-specific values:

```typescript
// Business domain branded types
export const ServiceTypeNameSchema = Schema.String.pipe(
  Schema.minLength(2),
  Schema.maxLength(100),
  Schema.brand('ServiceTypeName'),
  Schema.annotations({
    title: 'Service Type Name',
    description: 'A validated service type name'
  })
);

export const TagSchema = Schema.String.pipe(
  Schema.minLength(1),
  Schema.maxLength(20),
  Schema.pattern(/^[a-zA-Z0-9-_]+$/),
  Schema.transform(Schema.String, {
    strict: true,
    decode: (s) => s.toLowerCase().trim(),
    encode: (s) => s
  }),
  Schema.brand('Tag'),
  Schema.annotations({
    title: 'Tag',
    description: 'A normalized tag for categorization'
  })
);

export type ServiceTypeName = Schema.Schema.Type<typeof ServiceTypeNameSchema>;
export type Tag = Schema.Schema.Type<typeof TagSchema>;
```

## Class-Based Schema Patterns

### Domain Entity Classes

Use `Schema.Class` for complex domain entities:

```typescript
import { Schema } from 'effect';
import { ActionHashSchema, AgentPubKeySchema, TimestampSchema } from './holochain.schemas';

// Core domain entity
export class ServiceTypeInDHT extends Schema.Class<ServiceTypeInDHT>('ServiceTypeInDHT')({
  name: Schema.String.pipe(
    Schema.minLength(2),
    Schema.maxLength(100),
    Schema.annotations({
      title: 'Service Type Name',
      description: 'The name of the service type'
    })
  ),
  description: Schema.String.pipe(
    Schema.minLength(10),
    Schema.maxLength(500),
    Schema.annotations({
      title: 'Service Type Description', 
      description: 'Detailed description of the service type'
    })
  ),
  tags: Schema.Array(Schema.String.pipe(Schema.minLength(1), Schema.maxLength(50))).pipe(
    Schema.annotations({
      title: 'Service Type Tags',
      description: 'Tags associated with the service type'
    })
  )
}) {}

// UI-enhanced entity class
export class UIServiceType extends Schema.Class<UIServiceType>('UIServiceType')({
  // Include all core fields
  name: Schema.String.pipe(Schema.minLength(2), Schema.maxLength(100)),
  description: Schema.String.pipe(Schema.minLength(10), Schema.maxLength(500)),
  tags: Schema.Array(Schema.String.pipe(Schema.minLength(1), Schema.maxLength(50))),
  
  // Add UI-specific metadata
  original_action_hash: Schema.optional(ActionHashSchema),
  previous_action_hash: Schema.optional(ActionHashSchema),
  creator: Schema.optional(AgentPubKeySchema),
  created_at: Schema.optional(TimestampSchema),
  updated_at: Schema.optional(TimestampSchema),
  status: Schema.Literal('pending', 'approved', 'rejected').pipe(
    Schema.annotations({
      title: 'Service Type Status',
      description: 'The approval status of the service type'
    })
  )
}) {}
```

### Input and Output Classes

Create dedicated classes for API inputs and outputs:

```typescript
// Input schemas for API calls
export class CreateEntityInput extends Schema.Class<CreateEntityInput>('CreateEntityInput')({
  name: Schema.String.pipe(Schema.minLength(1), Schema.maxLength(100)),
  description: Schema.String.pipe(Schema.minLength(10), Schema.maxLength(1000)),
  tags: Schema.Array(Schema.String.pipe(Schema.minLength(1), Schema.maxLength(50)))
}) {}

export class UpdateEntityInput extends Schema.Class<UpdateEntityInput>('UpdateEntityInput')({
  original_action_hash: ActionHashSchema,
  previous_action_hash: ActionHashSchema,
  updated_entity: CreateEntityInput
}) {}

// Collection and response schemas
export class EntitiesCollection extends Schema.Class<EntitiesCollection>('EntitiesCollection')({
  pending: Schema.Array(RecordSchema),
  approved: Schema.Array(RecordSchema),
  rejected: Schema.Array(RecordSchema)
}) {}

export class PaginatedEntities extends Schema.Class<PaginatedEntities>('PaginatedEntities')({
  entities: Schema.Array(UIEntitySchema),
  pagination: PaginationSchema
}) {}
```

## Form Validation Patterns

### Field-Level Validation Schemas

Create reusable validation schemas for common fields:

```typescript
import { Schema } from 'effect';

// Basic field validators
export const EmailSchema = Schema.String.pipe(
  Schema.minLength(1, { message: () => 'Email is required' }),
  Schema.pattern(/^[^\s@]+@[^\s@]+\.[^\s@]+$/, {
    message: () => 'Please enter a valid email address'
  }),
  Schema.annotations({
    title: 'Email Address',
    description: 'A valid email address'
  })
);

export const PasswordSchema = Schema.String.pipe(
  Schema.minLength(8, { message: () => 'Password must be at least 8 characters' }),
  Schema.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, {
    message: () => 'Password must contain at least one lowercase letter, one uppercase letter, and one number'
  }),
  Schema.annotations({
    title: 'Password',
    description: 'A secure password with minimum requirements'
  })
);

// Domain-specific field validators
export const TitleSchema = Schema.Trim.pipe(
  Schema.minLength(1, { message: () => 'Title is required' }),
  Schema.maxLength(100, { message: () => 'Title must be less than 100 characters' }),
  Schema.annotations({
    title: 'Title',
    description: 'Title for a request or offer'
  })
);

export const DescriptionSchema = Schema.Trim.pipe(
  Schema.minLength(10, { message: () => 'Description must be at least 10 characters' }),
  Schema.maxLength(1000, { message: () => 'Description must be less than 1000 characters' }),
  Schema.annotations({
    title: 'Description',
    description: 'Detailed description for a request or offer'
  })
);
```

### Complex Form Schemas

Combine field validators into complete form schemas:

```typescript
// Form data schemas with validation
export const CreateRequestFormSchema = Schema.Struct({
  title: TitleSchema,
  description: DescriptionSchema,
  tags: Schema.optional(TagsSchema).pipe(Schema.withDecodingDefault(() => [])),
  urgency: Schema.optional(Schema.Literal('low', 'medium', 'high')),
  location: Schema.optional(Schema.String.pipe(Schema.maxLength(100)))
}).pipe(
  Schema.annotations({
    title: 'Create Request Form',
    description: 'Form data for creating a new request'
  })
);

// Form with cross-field validation
export const RegisterFormSchema = Schema.Struct({
  username: UsernameSchema,
  email: EmailSchema,
  password: PasswordSchema,
  confirmPassword: Schema.String.pipe(
    Schema.minLength(1, { message: () => 'Please confirm your password' })
  )
}).pipe(
  Schema.filter((data) => 
    data.password === data.confirmPassword ? undefined : 'Passwords do not match'
  ),
  Schema.annotations({
    title: 'Register Form',
    description: 'Form data for user registration'
  })
);
```

## UI State Schema Patterns

### State Management Schemas

Create schemas for UI state validation:

```typescript
// Loading and async state schemas
export const LoadingStateSchema = Schema.Literal('idle', 'loading', 'success', 'error').pipe(
  Schema.annotations({
    title: 'Loading State',
    description: 'The current loading state of an async operation'
  })
);

// Modal and dialog state
export const ModalStateSchema = Schema.Struct({
  isOpen: Schema.Boolean,
  title: Schema.optional(Schema.String),
  size: Schema.optional(Schema.Literal('sm', 'md', 'lg', 'xl')),
  closable: Schema.optional(Schema.Boolean).pipe(Schema.withDecodingDefault(() => true)),
  data: Schema.optional(Schema.Unknown)
}).pipe(
  Schema.annotations({
    title: 'Modal State',
    description: 'State configuration for modals and dialogs'
  })
);

// Pagination schema
export const PaginationSchema = Schema.Struct({
  page: Schema.Number.pipe(Schema.int(), Schema.positive()),
  limit: Schema.Number.pipe(Schema.int(), Schema.positive(), Schema.lessThanOrEqualTo(100)),
  total: Schema.Number.pipe(Schema.int(), Schema.nonNegative()),
  hasNext: Schema.Boolean,
  hasPrev: Schema.Boolean
}).pipe(
  Schema.annotations({
    title: 'Pagination',
    description: 'Pagination information for lists'
  })
);
```

### Search and Filter Schemas

```typescript
// Search functionality schemas
export const SearchFiltersSchema = Schema.Struct({
  query: Schema.optional(Schema.String),
  tags: Schema.optional(Schema.Array(Schema.String.pipe(Schema.nonEmptyString()))),
  dateRange: Schema.optional(
    Schema.Struct({
      start: Schema.Number.pipe(Schema.int(), Schema.positive()),
      end: Schema.Number.pipe(Schema.int(), Schema.positive())
    })
  ),
  status: Schema.optional(Schema.Array(Schema.String.pipe(Schema.nonEmptyString())))
}).pipe(
  Schema.annotations({
    title: 'Search Filters',
    description: 'Filters to apply when searching'
  })
);

export const SortSchema = Schema.Struct({
  field: Schema.String.pipe(Schema.nonEmptyString()),
  direction: Schema.Literal('asc', 'desc')
}).pipe(
  Schema.annotations({
    title: 'Sort Configuration',
    description: 'How to sort a list of items'
  })
);
```

## Service Response Schemas

### API Response Validation

Create schemas for validating service responses:

```typescript
// Common response schemas
export const VoidResponseSchema = Schema.Void;
export const StringArraySchema = Schema.Array(Schema.String);
export const ActionHashArraySchema = Schema.Array(ActionHashSchema);

// Complex response schemas
export const TagStatisticsArraySchema = Schema.Array(
  Schema.Tuple(Schema.String, Schema.Number)
);

// Record-based responses
export const ServiceTypeRecordSchema = RecordSchema;
export const ServiceTypeRecordOrNullSchema = Schema.NullOr(ServiceTypeRecordSchema);
export const ServiceTypeRecordsArraySchema = Schema.Array(ServiceTypeRecordSchema);
```

### Transformation Schemas

Use schemas for data transformation:

```typescript
// Transform raw Holochain data to UI data
export const HolochainToUITransformSchema = Schema.transform(
  RecordSchema,
  UIServiceTypeSchema,
  {
    strict: true,
    decode: (record) => {
      // Transform logic here
      return transformRecordToUIServiceType(record);
    },
    encode: (uiServiceType) => {
      // Reverse transform logic here  
      return transformUIServiceTypeToRecord(uiServiceType);
    }
  }
);
```

## Schema Organization Patterns

### File Organization

Organize schemas by domain and complexity:

```typescript
// Domain-specific schema files
// ui/src/lib/schemas/domain-name.schemas.ts

// Core domain schemas
export class DomainEntity extends Schema.Class<DomainEntity>('DomainEntity')({
  // Core fields
}) {}

// UI-enhanced schemas
export class UIDomainEntity extends Schema.Class<UIDomainEntity>('UIDomainEntity')({
  // UI-specific fields
}) {}

// Input/output schemas
export class CreateDomainEntityInput extends Schema.Class<CreateDomainEntityInput>('CreateDomainEntityInput')({
  // Input fields
}) {}

// Response validation schemas
export const DomainEntityRecordSchema = RecordSchema;
export const DomainEntityArraySchema = Schema.Array(DomainEntityRecordSchema);

// Export types
export type DomainEntityType = Schema.Schema.Type<typeof DomainEntity>;
export type UIDomainEntityType = Schema.Schema.Type<typeof UIDomainEntity>;
```

### Centralized Exports

Export all schemas through [index.ts](mdc:ui/src/lib/schemas/index.ts):

```typescript
// Export all schemas from this directory
export * from './holochain.schemas';
export * from './ui.schemas';
export * from './form.schemas';
export * from './service-types.schemas';
// Add new domain schemas here
```

### Shared Schemas (`common.schemas.ts`)

To avoid code duplication, schemas shared across multiple domains **must** be placed in `ui/src/lib/schemas/common.schemas.ts`. This file serves as a repository for any schema that is not specific to a single domain.

-   **✅ DO**: Identify and extract schemas used by more than one domain (e.g., `TimePreferenceSchema`, `InteractionTypeSchema`) into `common.schemas.ts`.
-   **✅ DO**: Import these shared schemas from `common.schemas.ts` into the relevant domain-specific schema files (e.g., `requests.schemas.ts`, `offers.schemas.ts`).
-   **✅ DO**: Export `common.schemas.ts` from the main `index.ts` to make them available throughout the application.
-   **❌ DON'T**: Define the same schema in multiple domain-specific files.

**Example:**
```typescript
// ui/src/lib/schemas/common.schemas.ts
import { Schema as S } from 'effect';

export const TimePreferenceSchema = S.struct({
  // ... schema definition
});

// ui/src/lib/schemas/requests.schemas.ts
import { TimePreferenceSchema } from './common.schemas.ts';

export const RequestSchema = S.struct({
  // ... other fields
  time_preference: TimePreferenceSchema,
});

// ui/src/lib/schemas/index.ts
export * from './holochain.schemas';
export * from './ui.schemas';
export * from './form.schemas';
export * from './common.schemas'; // <-- Add export here
export * from './service-types.schemas';
export * from './requests.schemas.ts';
export * from './offers.schemas.ts';
// ...
```

### Import Pattern

Always import schemas from the centralized index:

```typescript
// ✅ Correct - Use centralized import
import { 
  ActionHashSchema,
  UIServiceType, 
  CreateRequestFormSchema 
} from '$lib/schemas';

// ❌ Incorrect - Direct file imports
import { ActionHashSchema } from '$lib/schemas/holochain.schemas';
```

## Schema Usage Strategy

### Validation Boundaries

Apply schemas strategically at appropriate boundaries:

```typescript
// ✅ Service boundaries - validate complex responses
const getTagStatistics = (): E.Effect<Array<[string, number]>, ServiceError> =>
  pipe(
    holochainClient.callZomeEffect(
      'service_types',
      'get_tag_statistics', 
      null,
      TagStatisticsArraySchema
    ),
    E.mapError((error) => ServiceError.fromError(error, 'Failed to get tag statistics'))
  );

// ✅ Form validation - validate user input
const validateForm = (formData: unknown): E.Effect<CreateRequestForm, ValidationError> =>
  pipe(
    Schema.decodeUnknown(CreateRequestFormSchema)(formData),
    E.mapError((error) => ValidationError.create('Form validation failed', error))
  );

// ❌ Avoid over-validation - don't validate simple pass-through data
const getRecord = (hash: ActionHash): E.Effect<Record | null, ServiceError> =>
  pipe(
    holochainClient.callZomeRawEffect('domain', 'get_record', hash),
    E.map((result) => result as Record | null), // Simple type assertion
    E.mapError((error) => ServiceError.fromError(error, 'Failed to get record'))
  );
```

## Best Practices

### ✅ DO:
- **Use Effect Schema**: Always use `Schema` from 'effect' package
- **Brand Important Types**: Use branded types for domain-specific values
- **Centralized Exports**: Export all schemas through index file
- **Meaningful Annotations**: Add title and description annotations
- **Strategic Validation**: Apply validation at appropriate boundaries
- **Class-Based Complex Types**: Use `Schema.Class` for complex entities
- **Type Exports**: Export both schemas and derived types

### ❌ DON'T:
- **External Schema Libraries**: Don't use external schema packages like `@effect/schema`
- **Over-Validation**: Don't validate simple pass-through data
- **Direct File Imports**: Don't import schemas directly from files
- **Generic Types**: Don't use generic types where branded types provide safety
- **Validation Everywhere**: Don't validate at every layer - be strategic

## Schema Testing Patterns

```typescript
// Test schema validation
describe('ServiceTypeSchema', () => {
  it('should validate valid service type', () => {
    const validData = {
      name: 'Test Service',
      description: 'A test service type',
      tags: ['test', 'development']
    };
    
    const result = Schema.decodeSync(ServiceTypeInDHT)(validData);
    expect(result.name).toBe('Test Service');
  });

  it('should reject invalid data', () => {
    const invalidData = {
      name: '', // Too short
      description: 'Short', // Too short
      tags: []
    };
    
    expect(() => Schema.decodeSync(ServiceTypeInDHT)(invalidData))
      .toThrow();
  });
});

// Test form validation
describe('Form validation', () => {
  it('should validate complete form', async () => {
    const formData = {
      title: 'Test Request',
      description: 'This is a test request description',
      tags: ['help', 'development']
    };
    
    const result = await E.runPromise(
      Schema.decodeUnknown(CreateRequestFormSchema)(formData)
    );
    expect(result.title).toBe('Test Request');
  });
});
```

This schema system provides comprehensive type safety, validation, and data transformation capabilities while maintaining consistency with Effect TS patterns and supporting the application's domain-driven architecture.
