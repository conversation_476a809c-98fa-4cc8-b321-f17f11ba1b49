---
description: 
globs: ui/src/lib/stores/**
alwaysApply: false
---
# Store Event Bus and Communication Guidelines

This document outlines the strict architectural rules for communication between Svelte stores to ensure a decoupled, maintainable, and scalable state management layer. The primary mechanism for cross-store communication that results in state changes is the `storeEventBus`.

For implementation details on the stores themselves, refer to [store-effect-patterns.mdc](mdc:.cursor/rules/store-effect-patterns.mdc).

## Core Principles

### 1. Principle of State Ownership

Each store is the **single source of truth** for its specific domain. For example, `requests.store.svelte.ts` is exclusively responsible for managing the state of all requests.

### 2. No Direct Cross-Store Mutation (Strictly Enforced)

- A store **MUST NOT** directly call methods on another store that mutate its state.
- A store **MUST NOT** directly modify the exported state variables of another store.

**Anti-Pattern Example (Forbidden):**

```typescript
// From within users.store.svelte.ts
// ❌ FORBIDDEN: Directly mutating another store's state
administrationStore.allUsers = [...administrationStore.allUsers, newUser];
```

This creates tight coupling and makes the application state unpredictable and difficult to debug.

### 3. Read-Only Access is Permitted

A store is permitted to read data from another store for compositional purposes (e.g., displaying related data). This should only be for read-only operations that do not trigger state changes in the owning store.

**Example (Permitted):**

```typescript
// From within offers.store.svelte.ts
// ✅ OK: Reading data for display or local computation
const allRequests = requestsStore.requests;
// ... use allRequests for matching or display logic ...
```

### 4. Use the `storeEventBus` for Cross-Store Actions

When an action in one store needs to trigger a state change in another, the originating store **MUST** emit an event on the `storeEventBus`. The store that owns the state will then subscribe to this event and handle its own state update.

This ensures a decoupled, one-way data flow.

**Correct Event-Driven Workflow:**

1.  **Action**: `users.store.svelte.ts` successfully creates a new user.
2.  **Emission**: Instead of mutating `administration.store`, it emits an event: `storeEventBus.emit('user:created', { user: newUser })`.
3.  **Subscription**: `administration.store.svelte.ts` subscribes to the `user:created` event.
4.  **Reaction**: The event listener within `administration.store` receives the event and updates its own `allUsers` state array.

### 5. `storeEvents.ts` as the Central Contract

All possible cross-store events **MUST** be defined in [`ui/src/lib/stores/storeEvents.ts`](mdc:ui/src/lib/stores/storeEvents.ts). This file serves as the definitive, type-safe contract for all asynchronous, event-driven communication between stores.

- Event names should be clear and follow the `domain:event_name` convention (e.g., `request:created`, `organization:updated`, `user:status:changed`).
