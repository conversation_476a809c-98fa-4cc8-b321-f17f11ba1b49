---
description: 
globs: ui/tests/integration/**
alwaysApply: false
---
# Frontend Integration Testing

This document defines integration testing patterns for end-to-end UI flows, service integration testing, and cross-domain workflow validation in the frontend application.

## Integration Testing Philosophy

### 1. **End-to-End Workflow Testing**
- Test complete user journeys from UI interaction to backend integration
- Validate cross-domain interactions (Service Types → Requests → Offers)
- Verify data flow through the entire application stack

### 2. **Service Integration Testing**
- Test real service interactions with minimal mocking
- Validate service composition and dependency injection
- Test service error propagation and recovery

### 3. **Cross-Component Communication**
- Test store-to-store communication via EventBus
- Validate component-store integration patterns
- Test reactive state updates across component boundaries

## Testing Commands

```bash
# Run all integration tests
cd ui && bun test:integration

# Run specific integration test
cd ui && bun test:integration serviceTypes.test.ts

# Watch mode for development
cd ui && bun test:integration --watch
```

## Service Integration Testing

### **Service Layer Integration**
Located in `ui/tests/integration/`

**Core Integration Test Structure:**
```typescript
describe('ServiceName Integration', () => {
  let mockHolochainClient: ReturnType<typeof createMockHolochainClientService>;
  let serviceLayer: Layer.Layer<ServiceNameServiceTag>;

  beforeEach(() => {
    mockHolochainClient = createMockHolochainClientService();
    serviceLayer = Layer.merge(
      ServiceNameServiceLive,
      Layer.succeed(HolochainClientServiceTag, mockHolochainClient)
    );
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Complete Workflow Integration', () => {
    it('should handle full CRUD lifecycle', async () => {
      // Arrange
      const testEntity = createTestEntity();
      mockHolochainClient.callZomeEffect
        .mockResolvedValueOnce(createResponse)
        .mockResolvedValueOnce(updateResponse)
        .mockResolvedValueOnce(deleteResponse);

      // Act & Assert - Create
      const createResult = await runEffect(
        serviceCreate(testEntity).pipe(E.provide(serviceLayer))
      );
      expect(createResult).toBeDefined();

      // Act & Assert - Update  
      const updateResult = await runEffect(
        serviceUpdate(entityId, updatedEntity).pipe(E.provide(serviceLayer))
      );
      expect(updateResult).toBeDefined();

      // Act & Assert - Delete
      const deleteResult = await runEffect(
        serviceDelete(entityId).pipe(E.provide(serviceLayer))
      );
      expect(deleteResult).toBe(true);
    });

    it('should handle error propagation across service calls', async () => {
      // Arrange
      const serviceError = new Error('Backend service error');
      mockHolochainClient.callZomeEffect.mockRejectedValue(serviceError);

      // Act & Assert
      await expect(
        runEffect(
          serviceOperation(params).pipe(E.provide(serviceLayer))
        )
      ).rejects.toThrow('Backend service error');
    });
  });
});
```

### **Multi-Service Integration**
Test interactions between multiple services:

```typescript
describe('Cross-Service Integration', () => {
  let combinedServiceLayer: Layer.Layer<
    ServiceTypesServiceTag | RequestsServiceTag | OffersServiceTag
  >;

  beforeEach(() => {
    const mockClient = createMockHolochainClientService();
    combinedServiceLayer = Layer.merge(
      ServiceTypesServiceLive,
      RequestsServiceLive,
      OffersServiceLive,
      Layer.succeed(HolochainClientServiceTag, mockClient)
    );
  });

  it('should create request with service type linking', async () => {
    // Arrange
    const serviceType = createTestServiceType();
    const request = createTestRequest();
    
    // Act - Create service type first
    const serviceTypeResult = await runEffect(
      createServiceType(serviceType).pipe(E.provide(combinedServiceLayer))
    );

    // Act - Create request with service type
    const requestResult = await runEffect(
      createRequest(request, [serviceTypeResult.hash]).pipe(
        E.provide(combinedServiceLayer)
      )
    );

    // Assert
    expect(requestResult).toBeDefined();
    expect(requestResult.serviceTypes).toContain(serviceTypeResult.hash);
  });
});
```

## Store Integration Testing

### **Store-Service Integration**
Test complete store functionality with real service integration:

```typescript
describe('ServiceTypesStore Integration', () => {
  let store: ServiceTypesStore;
  let mockHolochainClient: ReturnType<typeof createMockHolochainClientService>;
  let mockEventBus: ReturnType<typeof createMockEventBus>;

  beforeEach(async () => {
    mockHolochainClient = createMockHolochainClientService();
    mockEventBus = createMockEventBus<ServiceTypesStoreEvents>();

    const serviceLayer = Layer.merge(
      ServiceTypesServiceLive,
      Layer.succeed(HolochainClientServiceTag, mockHolochainClient),
      mockEventBus.mockLayer
    );

    store = await runEffect(
      createServiceTypesStore().pipe(E.provide(serviceLayer))
    );
  });

  describe('Complete Store Workflows', () => {
    it('should handle service type suggestion workflow', async () => {
      // Arrange
      const serviceType = createTestServiceType();
      mockHolochainClient.callZomeEffect.mockResolvedValue(serviceType);

      // Act
      await runEffect(store.suggestServiceType(serviceType));

      // Assert
      expect(store.loading).toBe(false);
      expect(store.error).toBeNull();
      expect(mockEventBus.emitHistory).toContainEqual({
        event: 'serviceType:suggested',
        payload: expect.objectContaining({ serviceType })
      });
    });

    it('should handle moderation workflow', async () => {
      // Arrange
      const serviceTypeHash = createMockActionHash('test-hash');
      mockHolochainClient.callZomeEffect
        .mockResolvedValueOnce([]) // getPending
        .mockResolvedValueOnce(undefined) // approve
        .mockResolvedValueOnce([]) // getPending after approval
        .mockResolvedValueOnce([serviceType]); // getApproved

      // Act
      await runEffect(store.loadServiceTypes());
      await runEffect(store.approveServiceType(serviceTypeHash));

      // Assert
      expect(store.pendingServiceTypes).toHaveLength(0);
      expect(store.approvedServiceTypes).toHaveLength(1);
      expect(mockEventBus.emitHistory).toContainEqual({
        event: 'serviceType:approved',
        payload: expect.objectContaining({ serviceTypeHash })
      });
    });
  });
});
```

### **Cross-Store Communication**
Test EventBus-mediated communication between stores:

```typescript
describe('Cross-Store Communication', () => {
  let serviceTypesStore: ServiceTypesStore;
  let requestsStore: RequestsStore;
  let sharedEventBus: ReturnType<typeof createMockEventBus>;

  beforeEach(async () => {
    sharedEventBus = createMockEventBus<
      ServiceTypesStoreEvents & RequestsStoreEvents
    >();

    const mockClient = createMockHolochainClientService();
    const serviceLayer = Layer.merge(
      ServiceTypesServiceLive,
      RequestsServiceLive,
      Layer.succeed(HolochainClientServiceTag, mockClient),
      sharedEventBus.mockLayer
    );

    serviceTypesStore = await runEffect(
      createServiceTypesStore().pipe(E.provide(serviceLayer))
    );
    requestsStore = await runEffect(
      createRequestsStore().pipe(E.provide(serviceLayer))
    );
  });

  it('should update requests when service type is approved', async () => {
    // Arrange
    const serviceTypeHash = createMockActionHash('service-type');
    mockClient.callZomeEffect.mockResolvedValue(mockApprovalResponse);

    // Act
    await runEffect(serviceTypesStore.approveServiceType(serviceTypeHash));

    // Simulate event handling in requests store
    const serviceTypeApprovedEvent = sharedEventBus.emitHistory.find(
      event => event.event === 'serviceType:approved'
    );
    
    // Assert
    expect(serviceTypeApprovedEvent).toBeDefined();
    expect(serviceTypeApprovedEvent.payload.serviceTypeHash).toBe(serviceTypeHash);
  });
});
```

## UI Workflow Integration Testing

### **Complete User Journey Testing**
Test end-to-end user workflows:

```typescript
describe('Service Type Management Journey', () => {
  let managementComposable: ReturnType<typeof useServiceTypesManagement>;
  let mockStore: ServiceTypesStore;

  beforeEach(async () => {
    mockStore = await createMockStore();
    managementComposable = useServiceTypesManagement();
    // Inject mock store into composable context
  });

  it('should complete suggestion to approval workflow', async () => {
    // Act - Suggest service type
    await runEffect(
      managementComposable.actions.suggestServiceType(testServiceType)
    );

    // Assert - Service type is in pending state
    expect(managementComposable.state.pendingServiceTypes).toContainEqual(
      expect.objectContaining({ name: testServiceType.name })
    );

    // Act - Approve service type
    await runEffect(
      managementComposable.actions.approveServiceType(serviceTypeHash)
    );

    // Assert - Service type moved to approved
    expect(managementComposable.state.approvedServiceTypes).toContainEqual(
      expect.objectContaining({ name: testServiceType.name })
    );
    expect(managementComposable.state.pendingServiceTypes).not.toContainEqual(
      expect.objectContaining({ name: testServiceType.name })
    );
  });
});
```

### **Form Integration Testing**
Test complete form workflows with validation and submission:

```typescript
describe('Service Type Form Integration', () => {
  let formComposable: ReturnType<typeof useServiceTypeForm>;
  let mockValidation: ReturnType<typeof useFormValidation>;

  beforeEach(() => {
    formComposable = useServiceTypeForm();
    mockValidation = useFormValidation();
  });

  it('should handle complete form submission workflow', async () => {
    // Arrange
    const formData = {
      name: 'Web Development',
      description: 'Full-stack web development',
      category: 'Technology',
      tags: ['javascript', 'react', 'nodejs']
    };

    // Act - Fill form
    formComposable.state.formData = formData;

    // Act - Validate
    const validationResult = mockValidation.validateServiceType(formData);
    expect(validationResult.isValid).toBe(true);

    // Act - Submit
    await runEffect(formComposable.actions.submitForm());

    // Assert
    expect(formComposable.state.submitting).toBe(false);
    expect(formComposable.state.submitSuccess).toBe(true);
    expect(formComposable.state.errors).toEqual({});
  });

  it('should handle form validation errors', async () => {
    // Arrange
    const invalidFormData = {
      name: '', // Invalid: empty name
      description: 'Valid description',
      category: 'Technology',
      tags: []
    };

    // Act
    formComposable.state.formData = invalidFormData;
    const validationResult = mockValidation.validateServiceType(invalidFormData);

    // Assert
    expect(validationResult.isValid).toBe(false);
    expect(validationResult.errors).toHaveProperty('name');
  });
});
```

## Error Integration Testing

### **Error Propagation Testing**
Test error handling across the entire stack:

```typescript
describe('Error Propagation Integration', () => {
  it('should propagate service errors to UI', async () => {
    // Arrange
    const serviceError = new ServiceTypesError.CreateFailed({
      message: 'Backend validation failed',
      cause: new Error('Zome error')
    });

    mockHolochainClient.callZomeEffect.mockRejectedValue(serviceError);

    // Act
    await runEffect(store.createServiceType(testServiceType));

    // Assert
    expect(store.error).toEqual(serviceError);
    expect(store.loading).toBe(false);
  });

  it('should handle network errors gracefully', async () => {
    // Arrange
    const networkError = new ConnectionError.NetworkTimeout({
      message: 'Network request timed out'
    });

    mockHolochainClient.callZomeEffect.mockRejectedValue(networkError);

    // Act
    await runEffect(store.loadServiceTypes());

    // Assert
    expect(store.error).toBeInstanceOf(ConnectionError.NetworkTimeout);
    expect(store.serviceTypes).toEqual([]);
  });
});
```

## Performance Integration Testing

### **Performance Validation**
Test performance characteristics of integrated workflows:

```typescript
describe('Performance Integration', () => {
  it('should handle large datasets efficiently', async () => {
    // Arrange
    const largeDataset = Array.from({ length: 1000 }, createTestServiceType);
    mockHolochainClient.callZomeEffect.mockResolvedValue(largeDataset);

    // Act
    const startTime = performance.now();
    await runEffect(store.loadServiceTypes());
    const endTime = performance.now();

    // Assert
    expect(endTime - startTime).toBeLessThan(2000); // < 2 seconds
    expect(store.serviceTypes).toHaveLength(1000);
    expect(store.loading).toBe(false);
  });

  it('should handle concurrent operations', async () => {
    // Arrange
    const concurrentOperations = Array.from({ length: 10 }, (_, i) =>
      store.createServiceType(createTestServiceType({ name: `Type ${i}` }))
    );

    // Act
    await runEffect(E.all(concurrentOperations, { concurrency: 5 }));

    // Assert
    expect(mockHolochainClient.callZomeEffect).toHaveBeenCalledTimes(10);
  });
});
```

## Testing Environment & Setup

### **Test Configuration**
Integration tests require more setup than unit tests:

```typescript
// Global setup for integration tests
beforeEach(async () => {
  // Reset all global state
  vi.clearAllMocks();
  
  // Setup mock services with realistic delays
  mockHolochainClient = createMockHolochainClientService({
    networkDelay: 100 // Simulate network latency
  });
  
  // Initialize stores with fresh state
  await initializeTestEnvironment();
});

afterEach(async () => {
  // Cleanup resources
  await cleanupTestEnvironment();
});
```

### **Performance Standards**
- **Target**: < 2 minutes total execution for all integration tests
- **Individual Tests**: < 10 seconds per complex workflow test
- **Concurrent Operations**: Support at least 5 concurrent operations

### **Debugging Integration Tests**
- Use detailed logging for service interactions
- Implement test-specific error contexts
- Track state changes across component boundaries
- Monitor EventBus message flow for debugging
