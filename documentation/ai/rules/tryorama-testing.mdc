---
description: 
globs: tests/**
alwaysApply: false
---
# Tryorama Backend Testing

This document defines the testing patterns for Holochain zomes using the Tryorama framework, covering multi-agent scenarios, data integrity, and business logic validation.

## Core Tryorama Patterns

### 1. **Multi-Agent Test Setup**
Use the standardized scenario runner for consistent multi-agent testing:

```typescript
import { runScenarioWithTwoAgents } from "../utils";

test("test description", async () => {
  await runScenarioWithTwoAgents(
    async (_scenario: Sc<PERSON><PERSON>, alice: Player, bob: Player) => {
      // Test implementation
    }
  );
}, { timeout: 180000 }); // 3 minutes timeout
```

### 2. **DHT Synchronization**
Always sync between agents after significant operations:

```typescript
// After creating/updating records
await dhtSync([alice, bob], alice.cells[0].cell_id[0]);

// Critical sync points:
// - After user/organization creation
// - After status updates
// - Before verification operations
// - After administrative actions
```

### 3. **Zome Function Calling Pattern**
```typescript
// Standard zome call structure
const result = await cell.callZome({
  zome_name: "domain_zome", // requests, offers, service_types, etc.
  fn_name: "function_name",
  payload: payloadObject
});
```

## Domain-Specific Testing Patterns

### **Administration Testing**
Located in `tests/src/requests_and_offers/administration/`

**Key Test Scenarios:**
- Network administrator registration and removal
- Entity status management (pending, accepted, rejected, suspended)
- Access control verification
- Temporary vs indefinite suspension workflows

**Common Patterns:**
```typescript
// Register administrator
await registerNetworkAdministrator(
  alice.cells[0], 
  userHash, 
  [alice.agentPubKey]
);

// Update entity status  
await updateEntityStatus(
  cell,
  AdministrationEntity.Users,
  entityHash,
  statusOriginalHash,
  statusPreviousHash,
  { status_type: "accepted" }
);
```

### **Service Types Testing**
Located in `tests/src/requests_and_offers/service-types-tests/`

**Test Coverage:**
- Service type CRUD operations
- Tag-based discovery and search
- Status moderation workflow (suggest → approve/reject)
- Access control and linking enforcement

**Status Testing Patterns:**
```typescript
// Suggestion workflow
const suggestion = await suggestServiceType(cell, { service_type });
const pending = await getPendingServiceTypes(cell);

// Moderation workflow  
await approveServiceType(cell, serviceTypeHash);
await rejectServiceType(cell, serviceTypeHash);
```

### **Users & Organizations Testing**
Located in `tests/src/requests_and_offers/users/` and `tests/src/requests_and_offers/organizations/`

**Key Scenarios:**
- User profile creation and updates
- Organization membership and coordination
- Multi-device agent relationships
- Permission verification

**Setup Patterns:**
```typescript
// Standard user creation
const user = sampleUser({ name: "Alice" });
const userRecord = await createUser(alice.cells[0], user);

// Organization with membership
const org = sampleOrganization({ name: "Test Org" });
const orgRecord = await createOrganization(alice.cells[0], org);
await addMemberToOrganization(alice.cells[0], orgHash, userHash);
```

### **Requests & Offers Testing**
Located in `tests/src/requests_and_offers/requests-tests/` and `tests/src/requests_and_offers/offers-tests/`

**Core Functionality:**
- CRUD operations with service type linking
- Organization-based requests/offers
- Creator and organization relationship tracking
- Administrator override capabilities

**Testing Pattern:**
```typescript
// Create with service type links
const request = sampleRequest();
const requestRecord = await createRequest(
  cell, 
  request, 
  organizationHash, 
  serviceTypeHashes
);

// Administrator operations
await updateRequest(adminCell, hash, hash, updatedRequest);
await deleteRequest(adminCell, requestHash);
```

## Testing Utilities & Helpers

### **Common Test Utilities**
Located in `tests/src/requests_and_offers/utils.ts`

**Essential Helpers:**
- `runScenarioWithTwoAgents()` - Standard multi-agent scenario
- `decodeRecords<T>()` and `decodeRecord<T>()` - Type-safe record decoding
- `extractWasmErrorMessage()` - Error message extraction
- `imagePathToArrayBuffer()` - File handling for tests

### **Domain-Specific Helpers**
Each domain has `common.ts` files with specialized functions:

**Sample Factories:**
```typescript
// Generate test data
const user = sampleUser({ name: "Custom Name" });
const org = sampleOrganization({ name: "Custom Org" });
const request = sampleRequest({ title: "Custom Title" });
```

**Domain Operations:**
```typescript
// Users domain
await createUser(cell, user, serviceTypeHashes);
await getUserAgents(cell, userHash);

// Organizations domain  
await addCoordinatorToOrganization(cell, orgHash, userHash);
await checkIfAgentIsOrganizationCoordinator(cell, orgHash);
```

## Error Testing Patterns

### **Access Control Verification**
```typescript
// Verify unauthorized access fails
await expect(
  unauthorizedOperation(bobCell, aliceResource)
).rejects.toThrow();

// Verify authorized access succeeds
await expect(
  authorizedOperation(aliceCell, aliceResource)
).resolves.toBeTruthy();
```

### **Permission Escalation Testing**
```typescript
// Test before admin privileges
await expect(operation(cell)).rejects.toThrow();

// Grant admin privileges
await registerNetworkAdministrator(cell, userHash, [agentPubKey]);
await dhtSync([alice, bob], cell.cell_id[0]);

// Test after admin privileges
await expect(operation(cell)).resolves.toBeTruthy();
```

## Testing Environment

### **Nix Development Environment**
Tryorama tests **MUST** be executed within the project's Nix development shell:

```bash
# Enter Nix environment
nix develop

# Run tests
cd tests
bun test
```

### **hApp Configuration**
Tests use the compiled hApp from `workdir/requests_and_offers.happ`:

```typescript
const hAppPath = process.cwd() + "/../workdir/requests_and_offers.happ";
const appSource = { appBundleSource: { path: hAppPath } };
```

## Performance & Reliability

### **Timeout Management**
- Standard timeout: `180000ms` (3 minutes) for complex multi-agent scenarios
- Critical sync points after each major operation
- Retry logic for flaky DHT propagation scenarios

### **Error Handling**
- Use structured error extraction with `extractWasmErrorMessage()`
- Implement proper assertion messages for debugging
- Test both success and failure scenarios comprehensively

### **Data Consistency**
- Verify data from multiple agent perspectives
- Test eventual consistency after DHT sync
- Validate link integrity across agent perspectives
