---
description: 
globs: 
alwaysApply: true
---
# System Architecture Guidelines

This rule outlines the high-level system architecture. For more detailed information, refer to the main documentation:
- [Architecture Overview](mdc:documentation/architecture.md)
- [Technical Specifications](mdc:documentation/technical-specs.md)

## Technology Stack
- **Backend**: <PERSON><PERSON><PERSON><PERSON> (Rust-based zomes - coordinator/integrity pattern)
- **Frontend**: SvelteKit, TailwindCSS, SkeletonUI
- **Runtime/Build**: Bun (TypeScript)
- **Async/Error Handling**: Effect TS (`effect`)
- **State Management**: Svelte 5 Runes & Stores (`$state`, `EntityCache`, `storeEventBus`)
- **Development Environment**: Nix shell (Only for DNA/Zome dev, builds, backend tests)

## Paradigm
- Agent-centric, distributed marketplace (integrating hREA)
- Decentralized exchange of skills and resources via Requests (Intents) & Offers (Proposals)
- Transparent, cryptographically secure interactions
- Modularity and composability
- Functional programming

## System Components
- **User Management**: Profiles (Advocate/Creator), hREA Agent integration, multi-device access.
- **Projects & Organizations**: hREA Agent representation (`classifiedAs`), team/member management, coordination.
- **Request/Offer System**: hREA Intent/Proposal creation, matching, skill association (ResourceSpecs), exchange tracking.
- **Administration**: Role-based access, entity verification, status management.

## Zome Structure (Example)
- **hrea_requests**: Handles hREA Intents (Requests).
- **hrea_offers**: Handles hREA Proposals (Offers).
- **hrea_resource_specifications**: Manages Skills/Categories.
- **users_organizations**: Profile management, organization/project agent handling, relationships.
- **administration**: Admin roles, verification, status management.

## Architecture Patterns
- **Service Layer**: Located in `ui/src/services/`. Wraps Holochain client calls using **Effect TS** for robust async operations and error handling. Use factory pattern for service creation.
- **State Management**: Svelte stores (`ui/src/lib/stores/`) using **Svelte 5 Runes** for reactivity. Utilize `EntityCache` within stores and `storeEventBus` for cross-store communication.
- **UI Components**: Located in `ui/src/lib/components/`, organized by feature. Use Svelte 5 patterns (Runes, `$props`, native events, `children` prop).
- **Data Mapping**: Map Holochain/hREA data to UI types (`ui/src/types/`) using mappers in `ui/src/utils/`.
- Clear separation of UI, state management, and service logic.
- Design for reusable, composable components and functions.
