---
description: 
globs: CHANGELOG.md
alwaysApply: false
---
# Changelog Maintenance Workflow

This document outlines the workflow for maintaining the CHANGELOG.md file in the requests-and-offers project.

## Overview

Our changelog follows [Keep a Changelog](mdc:https:/keepachangelog.com/en/1.0.0) format and adheres to [Semantic Versioning](mdc:https:/semver.org/spec/v2.0.0.html). Each entry should be meaningful to users and developers, providing clear insight into what changed between versions.

## Workflow Steps

### 1. Identify the Latest Release

First, determine the latest release tag to understand what changes need to be documented:---
description: 
globs: CHANGELOG.md
alwaysApply: false
---
# Changelog Maintenance Workflow

This document outlines the workflow for maintaining the CHANGELOG.md file in the requests-and-offers project.

## Overview

Our changelog follows [Keep a Changelog](mdc:https:/keepachangelog.com/en/1.0.0) format and adheres to [Semantic Versioning](mdc:https:/semver.org/spec/v2.0.0.html). Each entry should be meaningful to users and developers, providing clear insight into what changed between versions.

## Workflow Steps

### 1. Identify the Latest Release

First, determine the latest release tag to understand what changes need to be documented:

```bash
# Get the latest release tag
git describe --tags --abbrev=0

# Or list all tags to see release history
git tag --sort=-version:refname | head -10
```

### 2. Analyze Git History Since Last Release

Generate a comprehensive list of commits since the last release:

```bash
# Get all commits since the last release with detailed info
git log $(git describe --tags --abbrev=0)..HEAD --oneline --no-merges

# Get detailed commit info with files changed
git log $(git describe --tags --abbrev=0)..HEAD --stat --no-merges

# Get commits grouped by author (useful for attribution)
git shortlog $(git describe --tags --abbrev=0)..HEAD --no-merges
```

### 3. Analyze Git Diff Since Last Release

Review the overall changes to understand the scope of modifications:

```bash
# Get overall diff statistics
git diff $(git describe --tags --abbrev=0)..HEAD --stat

# Get diff for specific important files
git diff $(git describe --tags --abbrev=0)..HEAD --name-only | grep -E '\.(rs|ts|svelte|md)$'

# Check for breaking changes in key areas
git diff $(git describe --tags --abbrev=0)..HEAD -- dnas/ ui/src/lib/
```

### 4. Categorize Changes

Group commits into the following categories based on conventional commit patterns and impact:

#### Features
- New functionality added
- New components, services, or zomes
- New user-facing capabilities
- Keywords: `feat:`, `add:`, new files in components/services

#### Refactor
- Code restructuring without functional changes
- Performance improvements
- Code organization improvements
- Keywords: `refactor:`, `perf:`, file moves/renames

#### Fixed
- Bug fixes
- Error handling improvements
- UI/UX fixes
- Keywords: `fix:`, `bug:`, error handling

#### Testing
- New tests added
- Test improvements
- Testing infrastructure changes
- Keywords: `test:`, changes in `/tests/` directories

#### Documentation
- Documentation updates
- README changes
- Comment improvements
- Keywords: `docs:`, changes in `/documentation/`

#### Dependencies
- Package updates
- New dependencies
- Dependency removals
- Keywords: changes to `package.json`, `Cargo.toml`, `bun.lockb`

#### Maintenance
- Build system changes
- CI/CD improvements
- Development tooling
- Keywords: `chore:`, changes to configs, build files

### 5. Format Changelog Entries

Each entry should follow this format:

```markdown
- Brief description of the change (relates to #issue-number) (`commit-hash`).
```

Guidelines:
- Use present tense ("Add feature" not "Added feature")
- Be concise but descriptive
- Include issue numbers when applicable
- Always include the short commit hash (first 7 characters)
- Group related commits into single entries when appropriate

### 6. Determine Version Number

Follow semantic versioning:

- **Major (X.0.0)**: Breaking changes, incompatible API changes
- **Minor (0.X.0)**: New features, backwards compatible
- **Patch (0.0.X)**: Bug fixes, backwards compatible

For pre-release versions:
- **Alpha**: Early development, unstable features
- **Beta**: Feature complete, testing phase
- **RC**: Release candidate, final testing

### 7. Update CHANGELOG.md

Insert the new version section at the top of the changelog:

```markdown
## [X.Y.Z] - YYYY-MM-DD

### Features
- New feature descriptions with commit hashes

### Refactor
- Refactoring descriptions with commit hashes

### Fixed
- Bug fix descriptions with commit hashes

### Testing
- Testing improvements with commit hashes

### Documentation
- Documentation updates with commit hashes

### Dependencies
- Dependency changes with commit hashes

### Maintenance
- Maintenance changes with commit hashes
```

## Git Commands for Analysis

### Useful Commands for Changelog Preparation

```bash
# Get commits since last tag with conventional commit filtering
git log $(git describe --tags --abbrev=0)..HEAD --grep="feat:" --grep="fix:" --grep="refactor:" --grep="docs:" --grep="test:" --oneline --no-merges

# Get files changed by category
git diff $(git describe --tags --abbrev=0)..HEAD --name-only | sort | uniq

# Get commit count by type (for release notes)
git log $(git describe --tags --abbrev=0)..HEAD --no-merges --pretty=format:"%s" | grep -E "^(feat|fix|refactor|docs|test|chore):" | cut -d: -f1 | sort | uniq -c

# Check for breaking changes indicators
git log $(git describe --tags --abbrev=0)..HEAD --grep="BREAKING" --grep="breaking" --oneline --no-merges
```

### Automated Analysis Script

Create a script to automate the analysis:

```bash
#!/bin/bash
# scripts/analyze-changes.sh

LAST_TAG=$(git describe --tags --abbrev=0)
echo "Changes since $LAST_TAG:"
echo "========================"

echo -e "\n📊 Commit Statistics:"
git log $LAST_TAG..HEAD --no-merges --oneline | wc -l
echo "commits since last release"

echo -e "\n📁 Files Changed:"
git diff $LAST_TAG..HEAD --name-only | wc -l
echo "files modified"

echo -e "\n🏷️ Commit Types:"
git log $LAST_TAG..HEAD --no-merges --pretty=format:"%s" | grep -E "^(feat|fix|refactor|docs|test|chore):" | cut -d: -f1 | sort | uniq -c

echo -e "\n📝 Recent Commits:"
git log $LAST_TAG..HEAD --oneline --no-merges
```

## Quality Checklist

Before finalizing the changelog:

- [ ] All significant commits are included
- [ ] Entries are categorized correctly
- [ ] Commit hashes are accurate (7 characters)
- [ ] Issue numbers are referenced where applicable
- [ ] Breaking changes are clearly marked
- [ ] Version number follows semantic versioning
- [ ] Date is accurate (YYYY-MM-DD format)
- [ ] Grammar and spelling are correct
- [ ] Related commits are grouped logically

## Release Process Integration

1. **Pre-release**: Update changelog with upcoming version
2. **Tag creation**: Ensure changelog is complete before tagging
3. **Post-release**: Verify changelog accuracy
4. **Communication**: Use changelog for release notes and announcements

## Example Commit Analysis

For a commit like `feat: add service type search functionality (#42)`:
- **Category**: Features
- **Description**: "Implemented service type search functionality with autocomplete (relates to #42)"
- **Hash**: Include the 7-character commit hash

## Maintenance Schedule

- **Weekly**: Review recent commits for potential changelog entries
- **Pre-release**: Complete changelog update and review
- **Post-release**: Verify changelog accuracy and completeness
- **Quarterly**: Review and refine changelog processes 

```bash
# Get the latest release tag
git describe --tags --abbrev=0

# Or list all tags to see release history
git tag --sort=-version:refname | head -10
```

### 2. Analyze Git History Since Last Release

Generate a comprehensive list of commits since the last release:

```bash
# Get all commits since the last release with detailed info
git log $(git describe --tags --abbrev=0)..HEAD --oneline --no-merges

# Get detailed commit info with files changed
git log $(git describe --tags --abbrev=0)..HEAD --stat --no-merges

# Get commits grouped by author (useful for attribution)
git shortlog $(git describe --tags --abbrev=0)..HEAD --no-merges
```

### 3. Analyze Git Diff Since Last Release

Review the overall changes to understand the scope of modifications:

```bash
# Get overall diff statistics
git diff $(git describe --tags --abbrev=0)..HEAD --stat

# Get diff for specific important files
git diff $(git describe --tags --abbrev=0)..HEAD --name-only | grep -E '\.(rs|ts|svelte|md)$'

# Check for breaking changes in key areas
git diff $(git describe --tags --abbrev=0)..HEAD -- dnas/ ui/src/lib/
```

### 4. Categorize Changes

Group commits into the following categories based on conventional commit patterns and impact:

#### Features
- New functionality added
- New components, services, or zomes
- New user-facing capabilities
- Keywords: `feat:`, `add:`, new files in components/services

#### Refactor
- Code restructuring without functional changes
- Performance improvements
- Code organization improvements
- Keywords: `refactor:`, `perf:`, file moves/renames

#### Fixed
- Bug fixes
- Error handling improvements
- UI/UX fixes
- Keywords: `fix:`, `bug:`, error handling

#### Testing
- New tests added
- Test improvements
- Testing infrastructure changes
- Keywords: `test:`, changes in `/tests/` directories

#### Documentation
- Documentation updates
- README changes
- Comment improvements
- Keywords: `docs:`, changes in `/documentation/`

#### Dependencies
- Package updates
- New dependencies
- Dependency removals
- Keywords: changes to `package.json`, `Cargo.toml`, `bun.lockb`

#### Maintenance
- Build system changes
- CI/CD improvements
- Development tooling
- Keywords: `chore:`, changes to configs, build files

### 5. Format Changelog Entries

Each entry should follow this format:

```markdown
- Brief description of the change (relates to #issue-number) (`commit-hash`).
```

Guidelines:
- Use present tense ("Add feature" not "Added feature")
- Be concise but descriptive
- Include issue numbers when applicable
- Always include the short commit hash (first 7 characters)
- Group related commits into single entries when appropriate

### 6. Determine Version Number

Follow semantic versioning:

- **Major (X.0.0)**: Breaking changes, incompatible API changes
- **Minor (0.X.0)**: New features, backwards compatible
- **Patch (0.0.X)**: Bug fixes, backwards compatible

For pre-release versions:
- **Alpha**: Early development, unstable features
- **Beta**: Feature complete, testing phase
- **RC**: Release candidate, final testing

### 7. Update CHANGELOG.md

Insert the new version section at the top of the changelog:

```markdown
## [X.Y.Z] - YYYY-MM-DD

### Features
- New feature descriptions with commit hashes

### Refactor
- Refactoring descriptions with commit hashes

### Fixed
- Bug fix descriptions with commit hashes

### Testing
- Testing improvements with commit hashes

### Documentation
- Documentation updates with commit hashes

### Dependencies
- Dependency changes with commit hashes

### Maintenance
- Maintenance changes with commit hashes
```

## Git Commands for Analysis

### Useful Commands for Changelog Preparation

```bash
# Get commits since last tag with conventional commit filtering
git log $(git describe --tags --abbrev=0)..HEAD --grep="feat:" --grep="fix:" --grep="refactor:" --grep="docs:" --grep="test:" --oneline --no-merges

# Get files changed by category
git diff $(git describe --tags --abbrev=0)..HEAD --name-only | sort | uniq

# Get commit count by type (for release notes)
git log $(git describe --tags --abbrev=0)..HEAD --no-merges --pretty=format:"%s" | grep -E "^(feat|fix|refactor|docs|test|chore):" | cut -d: -f1 | sort | uniq -c

# Check for breaking changes indicators
git log $(git describe --tags --abbrev=0)..HEAD --grep="BREAKING" --grep="breaking" --oneline --no-merges
```

### Automated Analysis Script

Create a script to automate the analysis:

```bash
#!/bin/bash
# scripts/analyze-changes.sh

LAST_TAG=$(git describe --tags --abbrev=0)
echo "Changes since $LAST_TAG:"
echo "========================"

echo -e "\n📊 Commit Statistics:"
git log $LAST_TAG..HEAD --no-merges --oneline | wc -l
echo "commits since last release"

echo -e "\n📁 Files Changed:"
git diff $LAST_TAG..HEAD --name-only | wc -l
echo "files modified"

echo -e "\n🏷️ Commit Types:"
git log $LAST_TAG..HEAD --no-merges --pretty=format:"%s" | grep -E "^(feat|fix|refactor|docs|test|chore):" | cut -d: -f1 | sort | uniq -c

echo -e "\n📝 Recent Commits:"
git log $LAST_TAG..HEAD --oneline --no-merges
```

## Quality Checklist

Before finalizing the changelog:

- [ ] All significant commits are included
- [ ] Entries are categorized correctly
- [ ] Commit hashes are accurate (7 characters)
- [ ] Issue numbers are referenced where applicable
- [ ] Breaking changes are clearly marked
- [ ] Version number follows semantic versioning
- [ ] Date is accurate (YYYY-MM-DD format)
- [ ] Grammar and spelling are correct
- [ ] Related commits are grouped logically

## Release Process Integration

1. **Pre-release**: Update changelog with upcoming version
2. **Tag creation**: Ensure changelog is complete before tagging
3. **Post-release**: Verify changelog accuracy
4. **Communication**: Use changelog for release notes and announcements

## Example Commit Analysis

For a commit like `feat: add service type search functionality (#42)`:
- **Category**: Features
- **Description**: "Implemented service type search functionality with autocomplete (relates to #42)"
- **Hash**: Include the 7-character commit hash

## Maintenance Schedule

- **Weekly**: Review recent commits for potential changelog entries
- **Pre-release**: Complete changelog update and review
- **Post-release**: Verify changelog accuracy and completeness
- **Quarterly**: Review and refine changelog processes 