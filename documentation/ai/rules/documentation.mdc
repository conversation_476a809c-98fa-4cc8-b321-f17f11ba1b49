---
description: 
globs: documentation/*
alwaysApply: false
---
## Documentation Structure

The documentation resides in the `documentation/` folder. It follows a structure with core overview files at the top level and detailed content organized into subdirectories. README files within subdirectories enhance navigation, especially on GitHub.

```mermaid
flowchart TD
    subgraph TopLevel [documentation/]
        direction TB
        PO([project-overview.md])
        RQ([requirements.md])
        AR([architecture.md])
        TS([technical-specs.md])
        WIP([work-in-progress.md])
        ST([status.md])
        GD([guides/])
        AS([assets/])
        REQS_DIR([requirements/])
        ARCH_DIR([architecture/])
        TS_DIR([technical-specs/])
    end

    PO --> RQ
    PO --> AR
    PO --> TS
    PO --> GD
    PO --> AS
    
    RQ --> REQS_DIR
    AR --> ARCH_DIR
    TS --> TS_DIR

    subgraph RequirementsDir [requirements/]
        direction TB
        REQ_README([README.md])
        REQ_FILES[...details.md]
        REQ_README --> REQ_FILES
    end

    subgraph ArchDir [architecture/]
        direction TB
        ARCH_README([README.md])
        ARCH_FILES[...details.md]
        ARCH_README --> ARCH_FILES
    end

    subgraph TechSpecsDir [technical-specs/]
        direction TB
        TS_README([README.md])
        TS_FILES[...details.md]
        TS_ZOMES([zomes/])
        TS_README --> TS_FILES
        TS_README --> TS_ZOMES
    end

    subgraph GuidesDir [guides/]
        direction TB
        GD_README([README.md])
        GD_FILES[...guides.md]
        GD_README --> GD_FILES
    end

    REQS_DIR --> RequirementsDir
    ARCH_DIR --> ArchDir
    TS_DIR --> TechSpecsDir
    GD --> GuidesDir
```

### Core Files (Top Level)

1.  **[project-overview.md](mdc:documentation/project-overview.md)**: Foundational document summarizing scope, goals, and linking to core sections.
2.  **[requirements.md](mdc:documentation/requirements.md)**: High-level overview of requirements, linking to details in `requirements/`.
3.  **[architecture.md](mdc:documentation/architecture.md)**: High-level overview of system architecture, linking to details in `architecture/`.
4.  **[technical-specs.md](mdc:documentation/technical-specs.md)**: High-level overview of technical specifications, linking to details in `technical-specs/`.
5.  **[work-in-progress.md](mdc:documentation/work-in-progress.md)**: Tracks current work focus, recent changes, and next steps.
6.  **[status.md](mdc:documentation/status.md)**: Summarizes implementation status, known issues, and remaining tasks.

### Subdirectories

-   **[requirements/](mdc:documentation/requirements/README.md)**: Contains detailed requirement documents (features, MVP, roles, use cases). The `README.md` provides navigation for this section.
-   **[architecture/](mdc:documentation/architecture/README.md)**: Contains detailed architecture documents (overview, hREA integration). The `README.md` provides navigation for this section.
-   **[technical-specs/](mdc:documentation/technical-specs/README.md)**: Contains general technical specs, UI structure details, and the `zomes/` subdirectory. The `README.md` provides navigation for this section.
    -   **[zomes/](mdc:documentation/technical-specs/zomes/README.md)**: Contains detailed specifications for each zome. The `README.md` provides navigation for zome specs.
-   **[guides/](mdc:documentation/guides/README.md)**: Contains developer guides (getting started, installation, contributing). The `README.md` provides navigation for this section.
-   **[assets/](mdc:documentation/assets)**: Contains images and other media used in the documentation.

### Navigation

- Core files link down to subdirectory READMEs or specific detail files.
- Subdirectory READMEs provide context for their section, list contents, and link back up to the relevant core overview file.

## Core Workflows

### Ask Mode

```mermaid
flowchart TD
    Start[Start] --> ReadFiles[Read Documentation and Rules]
    ReadFiles --> CheckFiles{Files Complete?}
    
    CheckFiles -->|No| Plan[Create Plan]
    Plan --> Document[Document in Chat]
    
    CheckFiles -->|Yes| Verify[Verify Context]
    Verify --> Strategy[Develop Strategy]
    Strategy --> Present[Present Approach]
```

- **Read Documentation and Rules**: Analyze all files in `documentation/` and applicable rules in `.cursor/rules/` to understand project context.
- **Check Files**: If core files (`project-overview.md`, `requirements.md`, etc.) are missing, propose creating them based on existing documentation patterns.
- **Plan and Present**: Develop a strategy aligned with the documentation structure (linking core files to detailed subdirectories) and `.cursor/rules/`, presenting it for approval.

### Agent Mode

```mermaid
flowchart TD
    Start[Start] --> Context[Check Documentation and Rules]
    Context --> Update[Update Documentation]
    Update --> Rules[Update .cursor/rules/ if needed]
    Rules --> Execute[Execute Task]
    Execute --> Document[Document Changes]
```

- **Check Documentation and Rules**: Read all `documentation/` files and applicable `.cursor/rules/` files to establish context.
- **Update Documentation**: Document changes in appropriate files (e.g., `work-in-progress.md`, `status.md`, or specific detail files in subdirectories), updating relevant overview files or READMEs as needed, mimicking the existing style.
- **Update Rules**: Create or modify `.mdc` files in `.cursor/rules/` if new patterns emerge (like the README navigation pattern), ensuring consistency with existing rules and using the `.mdc` format.
- **Execute Task**: Implement tasks, ensuring code and documentation align with requirements (`requirements.md`), specifications (`technical-specs.md`, `architecture.md`), and `.cursor/rules/`.
- **Document Changes**: Update relevant files with changes, ensuring links in overview files and READMEs remain correct, following the existing format.

## Documentation Updates

Documentation updates occur when:

1. Discovering new project patterns or insights.
2. After implementing significant changes.
3. When the user requests with **update documentation** (MUST review ALL files in `documentation/`).
4. When context needs clarification or existing documentation lacks clarity.

```mermaid
flowchart TD
    Start[Update Process]
    
    subgraph Process
        P1[Review ALL Files]
        P2[Analyze Existing Patterns]
        P3[Document Current State]
        P4[Clarify Next Steps]
        P5[Update .cursor/rules/]
        
        P1 --> P2 --> P3 --> P4 --> P5
    end
    
    Start --> Process
```

- **Review ALL Files**: Examine every file in `documentation/` and applicable `.mdc` files in `.cursor/rules/` to ensure completeness and consistency.
- **Analyze Existing Patterns**: Identify the tone, structure, and style (e.g., overview files linking down, subdirectory READMEs linking up) of current documentation and rules.
- **Document Current State**: Update files to reflect changes, mimicking the established pattern and improving clarity or organization where possible.
- **Clarify Next Steps**: Ensure `work-in-progress.md` and `status.md` clearly outline future work in the established style.
- **Update .cursor/rules/**: Create or modify `.mdc` files in `.cursor/rules/` to capture new insights or patterns, aligning with the existing rules format.

**Note**: For **update documentation** requests, I MUST review all files, even if some don't require changes. Focus on `work-in-progress.md` and `status.md` for current state updates, ensuring improvements align with the existing documentation pattern.

## Project Intelligence (.cursor/rules/)

The `.cursor/rules/` folder contains project-specific rules in `.mdc` files, providing instructions for the Agent to follow when generating code or assisting with tasks. These rules enhance my effectiveness by codifying project patterns, preferences, and context, as per the Cursor Rules documentation.

```mermaid
flowchart TD
    Start{Discover New Pattern}
    
    subgraph Learn [Learning Process]
        D1[Identify Pattern]
        D2[Validate with User]
        D3[Document in .cursor/rules/]
    end
    
    subgraph Apply [Usage]
        A1[Read .cursor/rules/]
        A2[Apply Learned Patterns]
        A3[Improve Future Work]
    end
    
    Start --> Learn
    Learn --> Apply
```

### What to Capture

- Critical implementation paths (e.g., preferred file structures).
- User preferences (e.g., documentation style, update frequency).
- Project-specific patterns (e.g., naming conventions, testing approaches).
- Known challenges and solutions.
- Evolution of project decisions.
- Tool usage patterns.
- Documentation patterns (e.g., "Core overview files link to detail directories; detail directories have READMEs linking back up.")

### Rule File Format

- Rules are stored as `.mdc` files in `.cursor/rules/` (e.g., `base.mdc`, `frontend.mdc`).

- Each `.mdc` file includes metadata (e.g., description, globs, alwaysApply) and content, following the format:

  ```
  --- 
  description: Base project rules
  globs: **/* 
  alwaysApply: true 
  ---
  - Follow coding standards defined in @documentation/technical-specs.md
  - Use user story format for requirements in @documentation/requirements.md
  ```

- Use glob patterns to scope rules (e.g., `app/**/*.ts` for TypeScript files).

- Support rule types: "Always" (always applied), "Auto Attached" (applied when files match globs), "Agent Requested" (applied based on intent), or "Manual" (explicitly attached).

- Reference other files with `@file` (e.g., `@documentation/architecture.md`) for additional context.

- Support inheritance by referencing other `.mdc` files (e.g., `@base.mdc`).

### Documentation-Specific Rules

- Include insights about the preferred documentation style for requirements (detailed in `documentation/requirements/`) and specifications (detailed in `documentation/technical-specs/` and `documentation/architecture/`).
- Ensure rules reflect how to maintain consistency with the existing `documentation/` pattern (core files, detail subdirectories with READMEs).
- Example rule:

  ```
  --- 
  description: Documentation standards
  globs: documentation/**/*
  alwaysApply: false 
  ---
  - Mimic existing Markdown style in @documentation/ for new files
  - Use Mermaid diagrams in [documentation/architecture/overview.md](mdc:documentation/architecture/overview.md) for system design
  - Validate requirements in [documentation/requirements/](mdc:documentation/requirements/README.md) before updating specifications in [documentation/technical-specs/](mdc:documentation/technical-specs/README.md) or [documentation/architecture/](mdc:documentation/architecture/README.md)
  ```

### Management

- Create new rules via `Cmd + Shift + P > New Cursor Rule` or manually in `.cursor/rules/`.
- Update rules when new patterns emerge, ensuring they align with existing `.mdc` files.
- Use inheritance to keep rules modular (e.g., a `base.mdc` for general rules, `frontend.mdc` for frontend-specific rules).
- Store `.cursor/rules/` in the repository for version control and team consistency.

## Planning

When entering **Ask Mode**, I will:

1. Deeply analyze the requested changes and existing code to map the full scope.
2. Read all `documentation/` files (including overview files and subdirectory READMEs) and applicable `.mdc` files in `.cursor/rules/` to understand the current pattern and context.
3. Ask 4-6 clarifying questions based on my findings, ensuring alignment with the existing documentation structure (core files, subdirectories, README navigation), requirements/specifications framing, and `.cursor/rules/`.
4. Draft a comprehensive plan of action, including how documentation and `.cursor/rules/` will be updated to match the existing pattern with improvements.
5. Present the plan for approval, ensuring documentation changes are clearly outlined.

In **Agent Mode**, I will:

1. Implement the approved plan, ensuring all code and documentation changes align with the existing `documentation/` pattern, requirements/specifications, and `.cursor/rules/`.
2. After each phase/step, report what was completed, the next steps, and remaining phases.
3. Update `documentation/` files (e.g., `work-in-progress.md`, `status.md`) and `.cursor/rules/` to reflect changes, mimicking the existing style and improving clarity or structure where possible.