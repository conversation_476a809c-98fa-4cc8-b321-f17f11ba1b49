# Store Effect Patterns

This document defines the standardized Effect TS patterns for Svelte store implementation, based on the comprehensive patterns established in the Service Types, Requests, and Offers domain standardizations.

## Core Store Principles

- **Factory Function Pattern**: Stores are created via factory functions returning Effects
- **Svelte 5 Runes Integration**: Use `$state`, `$derived`, `$effect` for reactive state
- **Service Orchestration**: Stores orchestrate service calls through Effect pipelines
- **Helper Function Organization**: Standardized helper functions for code reduction and consistency
- **Eager Initialization**: Immediate, module-level store creation to prevent Svelte 5 reactivity conflicts.
- **Event Integration**: Standardized event emission patterns

## Store Architecture Pattern

### 1. File Structure Organization

```typescript
// ============================================================================
// CONSTANTS
// ============================================================================
const CACHE_EXPIRY_MS = 5 * 60 * 1000; // Domain-specific expiry

const ERROR_CONTEXTS = {
  CREATE_ENTITY: 'Failed to create entity',
  GET_ENTITY: 'Failed to get entity',
  UPDATE_ENTITY: 'Failed to update entity',
  DELETE_ENTITY: 'Failed to delete entity',
  GET_ALL_ENTITIES: 'Failed to get all entities',
  // ... all operation contexts
} as const;

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================
export type DomainStore = {
  readonly entities: UIEntity[];
  readonly loading: boolean;
  readonly error: string | null;
  readonly cache: EntityCacheService<UIEntity>;

  // Methods
  getEntity: (hash: ActionHash) => E.Effect<UIEntity | null, DomainStoreError>;
  getLatestEntity: (hash: ActionHash) => E.Effect<UIEntity, DomainStoreError>;
  fetchAllEntities: () => E.Effect<void, DomainStoreError>;
  // ... other methods
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================
// ============================================================================
// DATA FETCHING HELPERS
// ============================================================================
// ============================================================================
// ENTITY CREATION HELPERS
// ============================================================================
// ============================================================================
// STATE MANAGEMENT HELPERS
// ============================================================================
// ============================================================================
// EVENT EMISSION HELPERS
// ============================================================================
// ============================================================================
// STORE FACTORY FUNCTION
// ============================================================================
// ============================================================================
// STORE INSTANCE CREATION
// ============================================================================
```

### 2. Standardized Helper Functions

**Nine Core Helper Function Types** (based on Service Types pattern):

#### Entity Creation Helper

```typescript
/**
 * Creates a complete UI entity from a Holochain record, returning null if decoding fails.
 */
const createUIEntity = (
  record: Record,
  status: 'pending' | 'approved' | 'rejected' = 'approved'
): UIEntity | null => {
  if (
    !record ||
    !record.entry ||
    !(record.entry as HolochainEntry).Present ||
    !(record.entry as HolochainEntry).Present.entry
  ) {
    return null;
  }

  try {
    const decodedEntry = decode((record.entry as HolochainEntry).Present.entry) as EntityInDHT;

    return {
      ...decodedEntry,
      original_action_hash: record.signed_action.hashed.hash,
      previous_action_hash: record.signed_action.hashed.hash,
      creator: record.signed_action.hashed.content.author,
      created_at: record.signed_action.hashed.content.timestamp,
      updated_at: record.signed_action.hashed.content.timestamp,
      status
    };
  } catch (error) {
    console.error('Failed to decode entry:', error);
    return null;
  }
};
```

#### Record Mapping Helper

```typescript
/**
 * Maps records array to UI entities with consistent error handling.
 */
const mapRecordsToUIEntities = (
  recordsArray: Record[],
  status: 'pending' | 'approved' | 'rejected'
): UIEntity[] =>
  recordsArray
    .map((record) => createUIEntity(record, status))
    .filter((entity): entity is UIEntity => entity !== null);
```

#### Cache Synchronization Helper

```typescript
/**
 * Helper to synchronize cache with local state arrays.
 */
const createCacheSyncHelper = (
  entities: UIEntity[],
  pendingEntities: UIEntity[],
  approvedEntities: UIEntity[],
  rejectedEntities: UIEntity[]
) => {
  const syncCacheToState = (entity: UIEntity, operation: 'add' | 'update' | 'remove') => {
    const hash = entity.original_action_hash?.toString();
    if (!hash) return;

    const findAndRemoveFromArray = (array: UIEntity[]) => {
      const index = array.findIndex((e) => e.original_action_hash?.toString() === hash);
      if (index !== -1) {
        return array.splice(index, 1)[0];
      }
      return null;
    };

    const addToArray = (array: UIEntity[], item: UIEntity) => {
      const existingIndex = array.findIndex((e) => e.original_action_hash?.toString() === hash);
      if (existingIndex !== -1) {
        array[existingIndex] = item;
      } else {
        array.push(item);
      }
    };

    switch (operation) {
      case 'add':
      case 'update':
        findAndRemoveFromArray(entities);
        findAndRemoveFromArray(pendingEntities);
        findAndRemoveFromArray(approvedEntities);
        findAndRemoveFromArray(rejectedEntities);

        addToArray(entities, entity);
        switch (entity.status) {
          case 'pending':
            addToArray(pendingEntities, entity);
            break;
          case 'approved':
            addToArray(approvedEntities, entity);
            break;
          case 'rejected':
            addToArray(rejectedEntities, entity);
            break;
        }
        break;
      case 'remove':
        findAndRemoveFromArray(entities);
        findAndRemoveFromArray(pendingEntities);
        findAndRemoveFromArray(approvedEntities);
        findAndRemoveFromArray(rejectedEntities);
        break;
    }
  };

  return { syncCacheToState };
};
```

#### Event Emission Helpers

```typescript
/**
 * Creates standardized event emission helpers.
 */
const createEventEmitters = () => {
  const emitEntityCreated = (entity: UIEntity): E.Effect<void, never, never> =>
    pipe(
      E.gen(function* () {
        const eventBus = yield* StoreEventBusTag;
        yield* eventBus.emit('entity:created', { entity });
      }),
      E.catchAll(() => E.void),
      E.provide(StoreEventBusLive)
    ) as E.Effect<void, never, never>;

  const emitEntityUpdated = (entity: UIEntity): E.Effect<void, never, never> =>
    pipe(
      E.gen(function* () {
        const eventBus = yield* StoreEventBusTag;
        yield* eventBus.emit('entity:updated', { entity });
      }),
      E.catchAll(() => E.void),
      E.provide(StoreEventBusLive)
    ) as E.Effect<void, never, never>;

  const emitEntityDeleted = (entityHash: ActionHash): E.Effect<void, never, never> =>
    pipe(
      E.gen(function* () {
        const eventBus = yield* StoreEventBusTag;
        yield* eventBus.emit('entity:deleted', { entityHash });
      }),
      E.catchAll(() => E.void),
      E.provide(StoreEventBusLive)
    ) as E.Effect<void, never, never>;

  return {
    emitEntityCreated,
    emitEntityUpdated,
    emitEntityDeleted
  };
};
```

#### Data Fetching Helper

```typescript
/**
 * Creates a standardized function for fetching and mapping entities with state updates.
 */
const createEntitiesFetcher = (
  serviceMethod: () => E.Effect<Record[], unknown>,
  targetArray: UIEntity[],
  status: 'pending' | 'approved' | 'rejected',
  errorContext: string,
  setLoading: (loading: boolean) => void,
  setError: (error: string | null) => void
) =>
  withLoadingState(
    () =>
      pipe(
        serviceMethod(),
        E.map((records) => {
          const uiEntities = mapRecordsToUIEntities(records, status);
          targetArray.splice(0, targetArray.length, ...uiEntities);
          return uiEntities;
        }),
        E.catchAll((error) => {
          const errorMessage = String(error);
          if (errorMessage.includes('Client not connected')) {
            console.warn(
              `Holochain client not connected, returning empty ${status} entities array`
            );
            return E.succeed([]);
          }
          return E.fail(DomainStoreError.fromError(error, errorContext));
        })
      ),
    setLoading,
    setError
  );
```

#### Loading State Helper

```typescript
/**
 * Creates a higher-order function that wraps operations with loading/error state management.
 */
const withLoadingState = <A, E, R>(effect: E.Effect<A, E, R>) =>
  E.uninterruptible(
    pipe(
      E.sync(() => {
        // Implementation provided in the store factory
      }),
      E.flatMap(() => effect),
      E.tap(() => E.sync(() => {})),
      E.tapError(() => E.sync(() => {}))
    )
  );
```

#### Record Creation Helper

```typescript
/**
 * Creates a helper for record creation operations (create/suggest).
 */
const createRecordCreationHelper = (
  cache: EntityCacheService<UIEntity>,
  syncCacheToState: (entity: UIEntity, operation: 'add' | 'update' | 'remove') => void
) => {
  const processCreatedRecord = (
    record: Record,
    status: 'pending' | 'approved' | 'rejected' = 'pending'
  ) => {
    const newEntity = createUIEntity(record, status);
    if (newEntity) {
      E.runSync(cache.set(record.signed_action.hashed.hash.toString(), newEntity));
      syncCacheToState(newEntity, 'add');
    }
    return { record, newEntity };
  };

  return { processCreatedRecord };
};
```

#### Status Transition Helper

```typescript
/**
 * Creates a helper for status transition operations (approve/reject).
 */
const createStatusTransitionHelper = (
  pendingEntities: UIEntity[],
  approvedEntities: UIEntity[],
  rejectedEntities: UIEntity[],
  cache: EntityCacheService<UIEntity>
) => {
  const transitionEntityStatus = (
    entityHash: ActionHash,
    newStatus: 'approved' | 'rejected'
  ) => {
    const hashString = entityHash.toString();
    const pendingIndex = pendingEntities.findIndex(
      (e) => e.original_action_hash?.toString() === hashString
    );
    const rejectedIndex = rejectedEntities.findIndex(
      (e) => e.original_action_hash?.toString() === hashString
    );

    let entity: UIEntity | null = null;

    if (newStatus === 'approved') {
      if (pendingIndex !== -1) {
        entity = pendingEntities.splice(pendingIndex, 1)[0];
      } else if (rejectedIndex !== -1) {
        entity = rejectedEntities.splice(rejectedIndex, 1)[0];
      }

      if (entity) {
        entity.status = 'approved';
        approvedEntities.push(entity);
        E.runSync(cache.set(hashString, entity));
      }
    } else if (newStatus === 'rejected') {
      if (pendingIndex !== -1) {
        entity = pendingEntities.splice(pendingIndex, 1)[0];
        if (entity) {
          entity.status = 'rejected';
          rejectedEntities.push(entity);
          E.runSync(cache.set(hashString, entity));
        }
      }
    }
  };

  return { transitionEntityStatus };
};
```

#### Multiple Collection Processor

```typescript
/**
 * Processes multiple record collections and updates cache and state.
 */
const processMultipleRecordCollections = (
  collections: { pending: Record[]; approved: Record[]; rejected: Record[] },
  cache: EntityCacheService<UIEntity>,
  syncCacheToState: (entity: UIEntity, operation: 'add' | 'update' | 'remove') => void
): UIEntity[] => {
  const pendingUIEntities = mapRecordsToUIEntities(collections.pending, 'pending');
  const approvedUIEntities = mapRecordsToUIEntities(collections.approved, 'approved');
  const rejectedUIEntities = mapRecordsToUIEntities(collections.rejected, 'rejected');

  const allNewEntities = [
    ...pendingUIEntities,
    ...approvedUIEntities,
    ...rejectedUIEntities
  ];

  allNewEntities.forEach((entity) => {
    E.runSync(cache.set(entity.original_action_hash?.toString() || '', entity));
    syncCacheToState(entity, 'add');
  });

  return allNewEntities;
};
```

### 3. Store Factory Pattern

```typescript
/**
 * Factory function to create a domain store as an Effect.
 */
export const createDomainStore = (): E.Effect<
  DomainStore,
  never,
  DomainServiceTag | CacheServiceTag
> =>
  E.gen(function* () {
    const domainService = yield* DomainServiceTag;
    const cacheService = yield* CacheServiceTag;

    // ========================================================================
    // STATE INITIALIZATION
    // ========================================================================
    const entities: UIEntity[] = $state([]);
    const pendingEntities: UIEntity[] = $state([]);
    const approvedEntities: UIEntity[] = $state([]);
    const rejectedEntities: UIEntity[] = $state([]);
    let loading: boolean = $state(false);
    let error: string | null = $state(null);

    const withLoading = <A, E, R>(effect: E.Effect<A, E, R>) =>
      E.uninterruptible(
        pipe(
          E.sync(() => {
            loading = true;
            error = null;
          }),
          E.flatMap(() => effect),
          E.tap(() => E.sync(() => (loading = false))),
          E.tapError((err) => E.sync(() => (error = String(err))))
        )
      );

    // ========================================================================
    // HELPER INITIALIZATION
    // ========================================================================
    const { syncCacheToState } = createCacheSyncHelper(
      entities,
      pendingEntities,
      approvedEntities,
      rejectedEntities
    );

    const {
      emitEntityCreated,
      emitEntityUpdated,
      emitEntityDeleted
    } = createEventEmitters();

    const { processCreatedRecord } = createRecordCreationHelper(cache, syncCacheToState);
    const { transitionEntityStatus } = createStatusTransitionHelper(
      pendingEntities,
      approvedEntities,
      rejectedEntities,
      cache
    );

    // ========================================================================
    // CACHE AND DATA FETCHING LOGIC
    // ========================================================================
    const lookupEntity = (hash: ActionHash): E.Effect<UIEntity, DomainStoreError> =>
      pipe(
        domainService.getEntity(hash),
        E.map((record) => (record ? createUIEntity(record) : null)),
        E.flatMap(E.fromNullable),
        E.catchAll((error) => E.fail(DomainStoreError.fromError(error, ERROR_CONTEXTS.GET_ENTITY)))
      );

    const cache = yield* cacheService.createEntityCache<UIEntity>(
      { expiryMs: CACHE_EXPIRY_MS, debug: false },
      lookupEntity
    );

    const fetchPendingEntities = createEntitiesFetcher(
      domainService.getPendingEntities,
      pendingEntities,
      'pending',
      ERROR_CONTEXTS.GET_ALL_ENTITIES,
      (val) => (loading = val),
      (val) => (error = val)
    );
    const fetchApprovedEntities = createEntitiesFetcher(
      domainService.getApprovedEntities,
      approvedEntities,
      'approved',
      ERROR_CONTEXTS.GET_ALL_ENTITIES,
      (val) => (loading = val),
      (val) => (error = val)
    );
    const fetchRejectedEntities = createEntitiesFetcher(
      domainService.getRejectedEntities,
      rejectedEntities,
      'rejected',
      ERROR_CONTEXTS.GET_ALL_ENTITIES,
      (val) => (loading = val),
      (val) => (error = val)
    );

    const fetchAllEntities = (): E.Effect<void, DomainStoreError> =>
      withLoading(
        pipe(
          E.all(
            [fetchPendingEntities(), fetchApprovedEntities(), fetchRejectedEntities()],
            { concurrency: 'inherit' }
          ),
          E.map((results) => {
            const allFetchedEntities = results.flat();
            entities.splice(0, entities.length, ...allFetchedEntities);
            allFetchedEntities.forEach((entity) =>
              E.runSync(cache.set(entity.original_action_hash.toString(), entity))
            );
          }),
          E.asVoid()
        )
      );

    // ========================================================================
    // STORE METHODS
    // ========================================================================
    const getEntity = (hash: ActionHash): E.Effect<UIEntity | null, DomainStoreError> =>
      withLoading(
        pipe(
          cache.get(hash.toString()),
          E.catchAll((error) => E.fail(DomainStoreError.fromError(error, ERROR_CONTEXTS.GET_ENTITY)))
        )
      );

    const getLatestEntity = (
      originalActionHash: ActionHash
    ): E.Effect<UIEntity, DomainStoreError> =>
      withLoading(
        pipe(
          domainService.getLatestEntity(originalActionHash),
          E.map((record) => createUIEntity(record)),
          E.flatMap(E.fromNullable),
          E.tap((latestEntity) =>
            pipe(
              cache.set(originalActionHash.toString(), latestEntity),
              E.flatMap(() => emitEntityUpdated(latestEntity)),
              E.runFork
            )
          ),
          E.catchAll((error) =>
            E.fail(DomainStoreError.fromError(error, ERROR_CONTEXTS.GET_ENTITY))
          )
        )
      );

    const createEntity = (entity: EntityInput): E.Effect<Record, DomainStoreError> =>
      withLoading(
        pipe(
          domainService.createEntity(entity),
          E.map((record) => processCreatedRecord(record, 'pending')),
          E.tap(({ newEntity }) => newEntity ? emitEntityCreated(newEntity) : E.void),
          E.map(({ record }) => record),
          E.catchAll((error) =>
            E.fail(DomainStoreError.fromError(error, ERROR_CONTEXTS.CREATE_ENTITY))
          )
        )
      );

    // ... other methods following same patterns

    // ========================================================================
    // STORE INTERFACE RETURN
    // ========================================================================
    return {
      get entities() { return entities; },
      get pendingEntities() { return pendingEntities; },
      get approvedEntities() { return approvedEntities; },
      get rejectedEntities() { return rejectedEntities; },
      get loading() { return loading; },
      get error() { return error; },
      get cache() { return cache; },

      createEntity,
      getEntity,
      getLatestEntity,
      // updateEntity,
      // deleteEntity,
      fetchAllEntities
    };
  });
```

### 4. Eager Initialization Pattern

This pattern ensures that the store is initialized immediately when the module is loaded, preventing reactive errors in Svelte 5 applications.

```typescript
// ============================================================================
// STORE INSTANCE CREATION
// ============================================================================
const domainStore: DomainStore = pipe(
  createDomainStore(),
  E.provide(DomainServiceLive),
  E.provide(CacheServiceLive),
  E.provide(HolochainClientLive),
  E.runSync
);

export default domainStore;
```

## Error Handling in Stores

### Store-Specific Tagged Errors

```typescript
export class DomainStoreError extends Data.TaggedError('DomainStoreError')<{
  message: string;
  context?: string;
  cause?: unknown;
}> {
  static fromError(error: unknown, context: string): DomainStoreError {
    if (error instanceof Error) {
      return new DomainStoreError({
        message: error.message,
        context,
        cause: error
      });
    }
    return new DomainStoreError({
      message: String(error),
      context,
      cause: error
    });
  }
}
```

## Best Practices

### ✅ DO:
- **Consistent Section Organization**: Use the 12-section structure for all stores.
- **Helper Function Consolidation**: Extract repeated patterns into the 9 standard helper functions.
- **ERROR_CONTEXTS Constants**: Centralize error context strings.
- **Event Emission**: Use standardized event emission patterns.
- **Eager Initialization**: Initialize stores at the module level to prevent Svelte 5 reactivity issues.
- **Cache Integration**: Implement consistent cache synchronization.
- **Loading State Management**: Use a local `withLoading` wrapper for consistent UX.

### ❌ DON'T:
- **Lazy Initialization**: Do not use a proxy-based lazy-loading pattern, as it conflicts with Svelte 5's reactivity model.
- **Manual State Synchronization**: Use helper functions instead of manual cache operations.
- **Hardcoded Error Messages**: Use ERROR_CONTEXTS constants.
- **Direct Effect Execution**: Always provide proper layers and error handling.
- **Infinite Reactive Loops**: Properly structure `$state` and `$derived` usage.
- **Mixed Patterns**: Follow the established 9-helper-function structure consistently.

This standardized pattern ensures all stores have consistent structure, comprehensive error handling, efficient code organization, and proper Effect integration while maintaining Svelte 5 reactivity.
