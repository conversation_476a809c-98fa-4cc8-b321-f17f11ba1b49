# Documentation

This directory contains comprehensive documentation for the Requests and Offers application.

## Overview

- [Project Overview](project-overview.md) - High-level introduction to the project
- [Requirements](requirements.md) - Project requirements and specifications
- [Architecture](architecture.md) - System architecture and design
- [Technical Specifications](technical-specs.md) - Detailed technical specifications
- [Work In Progress](work-in-progress.md) - Current development status
- [Status](status.md) - Project roadmap and milestone tracking

## Technical Documentation

### UI Architecture

- [UI Structure](technical-specs/ui-structure.md) - Overview of UI architecture
- [Component Library](technical-specs/component-library.md) - Component inventory and usage
- [State Management](technical-specs/state-management.md) - Svelte stores with Effect TS
- [Services Layer](technical-specs/services-layer.md) - Backend communication
- [UI Types and Schemas](technical-specs/ui-types.md) - Type system and validation
- [Event Bus Pattern](technical-specs/event-bus-pattern.md) - Event-based communication

### Backend Architecture

- [Zome Details](technical-specs/zomes/README.md) - Holochain zome implementation
- [hREA Integration](architecture/hrea-integration.md) - Resource-Event-Agent integration

## Development Guides

- [Getting Started](guides/getting-started.md) - First steps for new developers
- [Installation](guides/installation.md) - Setup instructions
- [Contributing](guides/contributing.md) - Contribution guidelines
