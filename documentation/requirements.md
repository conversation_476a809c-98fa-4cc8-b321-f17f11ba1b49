# Requirements Overview

This document outlines the project's requirements, covering user needs, features, and goals.

## Key Sections

- **[Features](./requirements/features.md)**: Detailed description of system features.
- **[MVP](./requirements/mvp.md)**: Minimum Viable Product scope and requirements.
- **[Roles](./requirements/roles.md)**: Definition of user roles and their capabilities.
- **[Use Cases](./requirements/use-cases.md)**: Description of user workflows and interactions. 