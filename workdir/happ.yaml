---
manifest_version: "1"
name: requests_and_offers
description: "Requests and offers distributed marketplace."
roles:
  - name: requests_and_offers
    provisioning:
      strategy: create
      deferred: false
    dna:
      bundled: "../dnas/requests_and_offers/workdir/requests_and_offers.dna"
      modifiers:
        network_seed: ~
        properties:
          progenitor_pubkey: "uhCAkVNjcdnXfoExk87X1hKArKH43bZnAidlsSgqBqeGvFpOPiUCT"
      installed_hash: ~
      clone_limit: 0
  - name: hrea
    provisioning:
      strategy: create
      deferred: false
    dna:
      bundled: "./hrea.dna"
      modifiers:
        network_seed: ~
        properties: ~
      installed_hash: ~
      clone_limit: 0
