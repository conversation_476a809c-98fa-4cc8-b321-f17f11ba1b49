---
manifest_version: "1"
name: requests_and_offers
integrity:
  network_seed: "requests_and_offers_alpha"
  properties: ~
  zomes:
    - name: users_organizations_integrity
      hash: ~
      bundled: "../target/wasm32-unknown-unknown/release/users_organizations_integrity.wasm"
      dependencies: ~
      dylib: ~
    - name: administration_integrity
      hash: ~
      bundled: "../target/wasm32-unknown-unknown/release/administration_integrity.wasm"
      dependencies: ~
      dylib: ~
    - name: requests_integrity
      hash: ~
      bundled: "../target/wasm32-unknown-unknown/release/requests_integrity.wasm"
      dependencies: ~
      dylib: ~
    - name: offers_integrity
      hash: ~
      bundled: "../target/wasm32-unknown-unknown/release/offers_integrity.wasm"
      dependencies: ~
      dylib: ~
    - name: service_types_integrity
      hash: ~
      bundled: "../target/wasm32-unknown-unknown/release/service_types_integrity.wasm"
      dependencies: ~
      dylib: ~
coordinator:
  zomes:
    - name: users_organizations
      hash: ~
      bundled: "../target/wasm32-unknown-unknown/release/users_organizations.wasm"
      dependencies:
        - name: users_organizations_integrity
      dylib: ~
    - name: administration
      hash: ~
      bundled: "../target/wasm32-unknown-unknown/release/administration.wasm"
      dependencies:
        - name: administration_integrity
      dylib: ~
    - name: requests
      hash: ~
      bundled: "../target/wasm32-unknown-unknown/release/requests.wasm"
      dependencies:
        - name: requests_integrity
      dylib: ~
    - name: offers
      hash: ~
      bundled: "../target/wasm32-unknown-unknown/release/offers.wasm"
      dependencies:
        - name: offers_integrity
      dylib: ~
    - name: misc
      hash: ~
      bundled: "../target/wasm32-unknown-unknown/release/misc.wasm"
      dependencies: []
      dylib: ~
    - name: service_types
      hash: ~
      bundled: "../target/wasm32-unknown-unknown/release/service_types.wasm"
      dependencies:
        - name: service_types_integrity
      dylib: ~
