{"name": "tests", "private": true, "scripts": {"test": "vitest run", "check": "eslint src --ext .ts", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix"}, "dependencies": {"@holochain/client": "^0.19.0", "@holochain/tryorama": "^0.18.2", "@msgpack/msgpack": "^2.8.0", "fflate": "^0.8.2", "js-base64": "3.7.7", "typescript": "^4.9.4", "vitest": "^0.28.4"}, "type": "module", "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "eslint": "^9.16.0", "vite": "^5.4.9"}}